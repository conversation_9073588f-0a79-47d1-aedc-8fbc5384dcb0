# وثيقة المتطلبات التقنية الشاملة
# تطبيق "دائن مدين" - نظام إدارة الديون الاحترافي

---

## 📋 جدول المحتويات

1. [نظرة عامة على التطبيق](#1-نظرة-عامة-على-التطبيق)
2. [المتطلبات الوظيفية](#2-المتطلبات-الوظيفية)
3. [المتطلبات التقنية](#3-المتطلبات-التقنية)
4. [تصميم واجهة المستخدم](#4-تصميم-واجهة-المستخدم)
5. [إدارة المستخدمين والأدوار](#5-إدارة-المستخدمين-والأدوار)
6. [إدارة البيانات](#6-إدارة-البيانات)
7. [التقارير والإحصائيات](#7-التقارير-والإحصائيات)
8. [متطلبات الأداء والجودة](#8-متطلبات-الأداء-والجودة)

---

## 1. نظرة عامة على التطبيق

### 🎯 الهدف من التطبيق

تطبيق "دائن مدين" هو نظام إدارة ديون شامل ومتطور يهدف إلى:

- **حل مشكلة إدارة الديون التقليدية**: استبدال الطرق اليدوية (الدفاتر والأوراق) بنظام رقمي متطور
- **تسهيل التتبع المالي**: توفير أدوات دقيقة لمتابعة الديون والمدفوعات
- **تحسين العلاقة التجارية**: تعزيز الثقة بين أصحاب المحلات والعملاء
- **الشفافية المالية**: توفير سجل واضح ومفصل لجميع المعاملات

### 👥 الجمهور المستهدف

#### أصحاب المحلات التجارية (Business Owners):
- محلات البقالة والسوبر ماركت
- المطاعم والمقاهي
- محلات الملابس والأحذية
- الصيدليات والعيادات
- ورش الصيانة والخدمات

#### العملاء (Customers):
- الزبائن الدائمون للمحلات
- العملاء الذين يفضلون الشراء بالآجل
- الأفراد الذين يحتاجون لتتبع مستحقاتهم

#### الموظفون (Employees):
- موظفو المحلات المخولون بتسجيل المعاملات
- المحاسبون والمسؤولون الماليون

### 🏆 المزايا التنافسية والقيمة المضافة

#### للمحلات التجارية:
- **إدارة مركزية**: تتبع جميع العملاء والديون في مكان واحد
- **تقارير تفصيلية**: إحصائيات دقيقة عن الأداء المالي
- **تذكيرات تلقائية**: إشعارات للعملاء بالمستحقات
- **نسخ احتياطية آمنة**: حماية البيانات من الفقدان
- **واجهة سهلة الاستخدام**: لا تحتاج خبرة تقنية

#### للعملاء:
- **شفافية كاملة**: رؤية واضحة لجميع المعاملات
- **تتبع المدفوعات**: سجل مفصل للدفعات السابقة
- **إشعارات ذكية**: تذكيرات بالمستحقات والعروض
- **كشوف حساب رقمية**: إمكانية طباعة وتصدير البيانات

#### مزايا تقنية متقدمة:
- **أمان عالي**: تشفير البيانات وحماية الخصوصية
- **عمل بدون إنترنت**: إمكانية العمل في حالة انقطاع الاتصال
- **مزامنة تلقائية**: تحديث البيانات عبر جميع الأجهزة
- **دعم متعدد اللغات**: واجهة باللغة العربية والإنجليزية

---

## 2. المتطلبات الوظيفية

### 🔐 نظام المصادقة والأمان

#### تسجيل الدخول:
- **تسجيل دخول بالبريد الإلكتروني وكلمة المرور**
- **تسجيل دخول بالبصمة** (للأجهزة المدعومة)
- **تسجيل دخول بـ Face ID** (للأجهزة المدعومة)
- **حفظ بيانات الدخول** (اختياري)
- **استعادة كلمة المرور** عبر البريد الإلكتروني

#### إنشاء الحسابات:
- **تسجيل حساب مالك منشأة جديد**
- **تسجيل حساب عميل جديد**
- **ربط العملاء بالمنشآت**
- **التحقق من البريد الإلكتروني**

### 🏪 وظائف مالك المنشأة

#### لوحة التحكم الرئيسية:
- **نظرة عامة مالية**: إجمالي الديون، المدفوعات، الأرباح
- **إحصائيات سريعة**: عدد العملاء، الديون المعلقة، المدفوعات اليومية
- **الأنشطة الأخيرة**: آخر المعاملات والتحديثات
- **تحية شخصية**: ترحيب باسم المالك مع الوقت المناسب
- **إشعارات مهمة**: تنبيهات للديون المستحقة والمدفوعات الجديدة

#### إدارة العملاء:
- **إنشاء حساب عميل جديد**:
  - إدخال بيانات العميل (الاسم، الهاتف، البريد، حد الائتمان)
  - تحديد اسم المستخدم وكلمة المرور للعميل
  - إرسال بيانات الدخول عبر SMS أو WhatsApp
  - لا يمكن للعملاء التسجيل الذاتي
- **عرض قائمة العملاء**: بحث وفلترة متقدمة
- **تفاصيل العميل**: سجل كامل للمعاملات والديون
- **تحديث بيانات العميل**: تعديل المعلومات الشخصية وبيانات الدخول
- **إعادة إرسال بيانات الدخول**: في حالة نسيان العميل لبياناته
- **حذف/إلغاء تفعيل العميل**: مع الاحتفاظ بالسجل التاريخي
- **تصدير بيانات العملاء**: Excel, PDF, CSV

#### إدارة الديون:
- **تسجيل دين جديد**: المبلغ، الوصف، التاريخ، العميل
- **عرض جميع الديون**: مع فلترة حسب الحالة والتاريخ
- **تحديث حالة الدين**: مدفوع، معلق، ملغي
- **تسجيل دفعة جزئية**: للديون الكبيرة
- **طباعة فاتورة الدين**: تصميم احترافي قابل للطباعة
- **إرسال تذكير للعميل**: عبر الإشعارات أو الرسائل

#### إدارة المدفوعات:
- **تسجيل دفعة جديدة**: ربط بالدين المقابل
- **عرض سجل المدفوعات**: مع تفاصيل كاملة
- **طباعة إيصال الدفع**: تصميم احترافي
- **إلغاء/تعديل المدفوعات**: مع تسجيل السبب

#### التقارير والإحصائيات:
- **تقرير الديون**: حسب الفترة الزمنية والعميل
- **تقرير المدفوعات**: تفصيلي أو إجمالي
- **تقرير الأرباح**: صافي الدخل والخسائر
- **تقرير العملاء**: الأكثر نشاطاً والأكثر ديوناً
- **إحصائيات شهرية/سنوية**: رسوم بيانية تفاعلية
- **تصدير التقارير**: PDF, Excel, Word

#### إدارة الاشتراك والفترة التجريبية:
- **الفترة التجريبية التلقائية**:
  - تفعيل فوري لمدة 30 يوم عند التسجيل الأول
  - وصول كامل لجميع الميزات خلال الفترة التجريبية
  - عرض واضح لحالة الاشتراك في لوحة التحكم
- **تذكيرات انتهاء الفترة التجريبية**:
  - إشعار قبل 7 أيام من الانتهاء
  - إشعار قبل 3 أيام من الانتهاء
  - إشعار قبل يوم واحد من الانتهاء
  - إشعار عند الانتهاء مع منع الوصول
- **شاشة طلب الاشتراك**:
  - عرض معرف الجهاز الفريد مع إمكانية النسخ
  - معلومات التواصل الكاملة (هاتف، واتساب، بريد إلكتروني)
  - تعليمات واضحة لخطوات الاشتراك
  - أزرار التواصل المباشر
- **مراقبة حالة الاشتراك**:
  - فحص دوري لحالة الاشتراك كل ساعة
  - تحديث فوري عند تفعيل الاشتراك
  - تسجيل خروج تلقائي عند انتهاء الاشتراك

### 👤 وظائف العميل

#### لوحة التحكم الشخصية:
- **نظرة عامة على الديون**: إجمالي المستحقات
- **آخر المعاملات**: الديون والمدفوعات الأخيرة
- **الرصيد الحالي**: المبلغ المستحق الدفع
- **تاريخ آخر دفعة**: مع تفاصيل المبلغ

#### عرض الديون:
- **قائمة الديون الحالية**: مع التفاصيل والتواريخ
- **تاريخ الديون**: جميع الديون السابقة
- **فلترة الديون**: حسب التاريخ والحالة والمبلغ
- **تفاصيل كل دين**: الوصف، التاريخ، المبلغ، الحالة

#### عرض المدفوعات:
- **سجل المدفوعات**: جميع الدفعات السابقة
- **إيصالات الدفع**: عرض وطباعة الإيصالات
- **إحصائيات الدفع**: إجمالي المدفوع شهرياً/سنوياً

#### طلب كشف حساب:
- **كشف حساب شامل**: جميع المعاملات
- **كشف حساب لفترة محددة**: حسب التاريخ
- **تصدير كشف الحساب**: PDF للطباعة أو الحفظ

### 👨‍💼 وظائف الموظف

#### صلاحيات محدودة:
- **تسجيل ديون جديدة**: حسب الصلاحيات المحددة
- **تسجيل مدفوعات**: للديون الموجودة
- **عرض بيانات العملاء**: قراءة فقط
- **طباعة الفواتير والإيصالات**: للمعاملات اليومية

---

## 3. المتطلبات التقنية

### 🛠️ التقنيات والأدوات المستخدمة

#### إطار العمل الأساسي:
- **Flutter 3.24+**: لتطوير التطبيق متعدد المنصات
- **Dart 3.0+**: لغة البرمجة الأساسية مع null safety

#### إدارة الحالة والتنقل:
- **GetX 4.6+**: لإدارة الحالة والتنقل والحقن
- **Obx**: للتحديثات التفاعلية للواجهة
- **GetStorage**: للتخزين المحلي البسيط
- **Get.find()**: لحقن التبعيات

#### التصميم والواجهة:
- **Material Design 3**: نظام التصميم الحديث
- **Custom Themes**: دعم الوضع الليلي/النهاري
- **Google Fonts**: خطوط عربية احترافية
- **RTL Support**: دعم كامل للكتابة من اليمين لليسار
- **Responsive Design**: تصميم متجاوب لجميع الأحجام

#### المصادقة والأمان (للمرحلة الحالية):
- **local_auth**: مصادقة بالبصمة و Face ID
- **crypto**: تشفير البيانات المحلية
- **device_info_plus**: معلومات الجهاز
- **shared_preferences**: حفظ الإعدادات

#### أدوات مساعدة:
- **intl**: دعم التواريخ والأرقام العربية
- **url_launcher**: فتح الروابط والتطبيقات الخارجية
- **image_picker**: اختيار الصور
- **pdf**: إنشاء ملفات PDF
- **excel**: إنشاء ملفات Excel

#### هيكل المشروع المفصل:
```
lib/
├── main.dart                 # نقطة البداية
├── app/
│   ├── bindings/            # ربط التبعيات
│   │   ├── initial_binding.dart
│   │   ├── auth_binding.dart
│   │   └── home_binding.dart
│   ├── controllers/         # تحكم في الحالة
│   │   ├── auth_controller.dart
│   │   ├── theme_controller.dart
│   │   ├── home_controller.dart
│   │   ├── customers_controller.dart
│   │   └── subscription_controller.dart
│   ├── data/               # نماذج البيانات
│   │   ├── models/
│   │   │   ├── user.dart
│   │   │   ├── customer.dart
│   │   │   ├── debt.dart
│   │   │   └── payment.dart
│   │   └── services/       # خدمات البيانات (محلية حالياً)
│   │       ├── local_storage_service.dart
│   │       ├── auth_service.dart
│   │       └── data_service.dart
│   ├── modules/            # الوحدات الرئيسية
│   │   ├── auth/
│   │   │   ├── views/
│   │   │   │   ├── login_screen.dart
│   │   │   │   └── splash_screen.dart
│   │   │   └── controllers/
│   │   │       └── auth_controller.dart
│   │   ├── home/
│   │   │   ├── views/
│   │   │   │   ├── home_screen.dart
│   │   │   │   └── dashboard_screen.dart
│   │   │   └── controllers/
│   │   │       └── home_controller.dart
│   │   ├── customers/
│   │   │   ├── views/
│   │   │   │   ├── customers_screen.dart
│   │   │   │   ├── add_customer_screen.dart
│   │   │   │   └── customer_details_screen.dart
│   │   │   └── controllers/
│   │   │       └── customers_controller.dart
│   │   └── subscription/
│   │       ├── views/
│   │       │   ├── subscription_screen.dart
│   │       │   └── subscription_expired_screen.dart
│   │       └── controllers/
│   │           └── subscription_controller.dart
│   ├── routes/             # التنقل والمسارات
│   │   ├── app_pages.dart
│   │   └── app_routes.dart
│   └── widgets/            # العناصر المشتركة
│       ├── custom_card.dart
│       ├── custom_button.dart
│       ├── custom_text_field.dart
│       ├── loading_indicator.dart
│       └── theme_toggle_button.dart
├── core/
│   ├── constants/          # الثوابت
│   │   ├── app_colors.dart
│   │   ├── app_strings.dart
│   │   └── app_constants.dart
│   ├── themes/            # الثيمات
│   │   ├── app_themes.dart
│   │   ├── light_theme.dart
│   │   └── dark_theme.dart
│   ├── utils/             # الأدوات المساعدة
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   ├── helpers.dart
│   │   └── extensions.dart
│   └── services/          # الخدمات الأساسية
│       ├── theme_service.dart
│       ├── storage_service.dart
│       └── device_service.dart
└── assets/
    ├── images/
    ├── icons/
    └── fonts/
```

#### ملف pubspec.yaml المحدث:
```yaml
name: dayen_madeen
description: نظام إدارة الديون الاحترافي
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter

  # إدارة الحالة والتنقل
  get: ^4.6.6
  get_storage: ^2.1.1

  # التصميم والواجهة
  google_fonts: ^6.1.0          # للخطوط العربية (Cairo, Tajawal)
  flutter_svg: ^2.0.9           # للأيقونات المخصصة SVG

  # المصادقة والأمان
  local_auth: ^2.1.7            # البصمة و Face ID
  crypto: ^3.0.3                # تشفير البيانات المحلية

  # معلومات الجهاز
  device_info_plus: ^9.1.1      # معرف الجهاز الفريد

  # أدوات مساعدة
  intl: ^0.18.1                 # دعم التواريخ والأرقام العربية
  url_launcher: ^6.2.2          # فتح الروابط والتطبيقات الخارجية
  image_picker: ^1.0.4          # اختيار الصور
  pdf: ^3.10.7                  # إنشاء ملفات PDF
  excel: ^2.1.0                 # إنشاء ملفات Excel

  # أيقونات إضافية
  cupertino_icons: ^1.0.6       # أيقونات iOS

  # مكتبات إضافية للنظام المركزي
  flutter_colorpicker: ^1.0.3   # منتقي الألوان (للإعدادات المتقدمة)

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # أدوات التطوير
  build_runner: ^2.4.7          # لتوليد الكود
  json_annotation: ^4.8.1       # للتعامل مع JSON

flutter:
  uses-material-design: true

  # الخطوط المخصصة (إذا لم نستخدم Google Fonts)
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700

  # الأصول (Assets)
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/

  # الأيقونات المخصصة
  # flutter_icons:
  #   android: "launcher_icon"
  #   ios: true
  #   image_path: "assets/icons/app_icon.png"
```

#### ملف analysis_options.yaml للحفاظ على جودة الكود:
```yaml
include: package:flutter_lints/flutter.yaml

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"

  strong-mode:
    implicit-casts: false
    implicit-dynamic: false

linter:
  rules:
    # قواعد إضافية لضمان جودة الكود
    prefer_const_constructors: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    avoid_print: true
    avoid_unnecessary_containers: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: true
    prefer_single_quotes: true
    require_trailing_commas: true
```

### 🗄️ هيكل قاعدة البيانات

#### جدول أصحاب المنشآت (business_owners):
```sql
CREATE TABLE business_owners (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    owner_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    profile_image_path TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول العملاء (customers):
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    business_owner_id UUID REFERENCES business_owners(id) ON DELETE CASCADE,
    auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    username VARCHAR(100) UNIQUE NOT NULL,
    user_type VARCHAR(20) DEFAULT 'customer',
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    current_balance DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول الديون (debts):
```sql
CREATE TABLE debts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    business_owner_id UUID REFERENCES business_owners(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    date_created TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_paid BOOLEAN DEFAULT false,
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول المدفوعات (payments):
```sql
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    debt_id UUID REFERENCES debts(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    business_owner_id UUID REFERENCES business_owners(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    payment_method VARCHAR(50) DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول الاشتراكات (subscriptions):
```sql
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    device_id VARCHAR(255) UNIQUE NOT NULL,
    business_owner_id UUID REFERENCES business_owners(id) ON DELETE CASCADE,
    business_name VARCHAR(255) NOT NULL,
    plan_type VARCHAR(50) NOT NULL DEFAULT 'trial', -- trial, premium, enterprise
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_trial BOOLEAN DEFAULT true,
    trial_start_date TIMESTAMP WITH TIME ZONE,
    trial_end_date TIMESTAMP WITH TIME ZONE,
    activated_by VARCHAR(100), -- admin, system, payment
    activated_at TIMESTAMP WITH TIME ZONE,
    last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول سجل الاشتراكات (subscription_logs):
```sql
CREATE TABLE subscription_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL, -- created, activated, expired, renewed, suspended
    old_status JSONB,
    new_status JSONB,
    performed_by VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### جدول إشعارات الاشتراك (subscription_notifications):
```sql
CREATE TABLE subscription_notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    subscription_id UUID REFERENCES subscriptions(id) ON DELETE CASCADE,
    notification_type VARCHAR(50) NOT NULL, -- reminder_7days, reminder_3days, reminder_1day, expired
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_read BOOLEAN DEFAULT false,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 🔒 سياسات الأمان (RLS Policies)

#### حماية بيانات أصحاب المنشآت:
```sql
-- أصحاب المنشآت يمكنهم رؤية بياناتهم فقط
CREATE POLICY "Business owners can view own data" ON business_owners
    FOR SELECT USING (auth_user_id = auth.uid());

CREATE POLICY "Business owners can update own data" ON business_owners
    FOR UPDATE USING (auth_user_id = auth.uid());
```

#### حماية بيانات العملاء:
```sql
-- العملاء يمكنهم رؤية بياناتهم فقط
CREATE POLICY "Customers can view own data" ON customers
    FOR SELECT USING (auth_user_id = auth.uid());

-- أصحاب المنشآت يمكنهم رؤية عملائهم فقط
CREATE POLICY "Business owners can view their customers" ON customers
    FOR SELECT USING (business_owner_id IN (
        SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
    ));
```

#### حماية بيانات الاشتراكات:
```sql
-- أصحاب المنشآت يمكنهم رؤية اشتراكهم فقط
CREATE POLICY "Business owners can view own subscription" ON subscriptions
    FOR SELECT USING (business_owner_id IN (
        SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
    ));

-- المشرفون فقط يمكنهم تعديل الاشتراكات
CREATE POLICY "Only admins can modify subscriptions" ON subscriptions
    FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- حماية سجل الاشتراكات
CREATE POLICY "Subscription logs read only for owners" ON subscription_logs
    FOR SELECT USING (subscription_id IN (
        SELECT id FROM subscriptions WHERE business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    ));

-- حماية إشعارات الاشتراك
CREATE POLICY "Subscription notifications for owners only" ON subscription_notifications
    FOR SELECT USING (subscription_id IN (
        SELECT id FROM subscriptions WHERE business_owner_id IN (
            SELECT id FROM business_owners WHERE auth_user_id = auth.uid()
        )
    ));
```

### 📱 APIs والخدمات الخارجية

#### خدمات Supabase:
- **Authentication API**: تسجيل الدخول والخروج
- **Database API**: عمليات CRUD على البيانات
- **Realtime API**: التحديثات الفورية
- **Storage API**: تخزين الصور والملفات

#### خدمات إضافية:
- **PDF Generation**: إنشاء الفواتير والتقارير
- **Push Notifications**: الإشعارات الفورية
- **Email Service**: إرسال كشوف الحساب
- **SMS Service**: تذكيرات نصية (اختياري)

### 🔐 متطلبات الأمان والخصوصية

#### تشفير البيانات:
- **تشفير البيانات أثناء النقل**: HTTPS/TLS 1.3
- **تشفير البيانات المخزنة**: AES-256
- **تشفير كلمات المرور**: bcrypt hashing
- **حماية الجلسات**: JWT tokens آمنة

#### الخصوصية:
- **سياسة الخصوصية**: واضحة ومفصلة
- **موافقة المستخدم**: على جمع واستخدام البيانات
- **حق الحذف**: إمكانية حذف البيانات الشخصية
- **تصدير البيانات**: إمكانية تصدير البيانات الشخصية

#### النسخ الاحتياطية:
- **نسخ احتياطية تلقائية**: يومية للبيانات الحساسة
- **نسخ احتياطية محلية**: على جهاز المستخدم
- **استعادة البيانات**: في حالة فقدان الجهاز
- **تشفير النسخ الاحتياطية**: حماية إضافية للبيانات

### 📱 متطلبات نظام إدارة الاشتراكات

#### خدمات معرف الجهاز:
```dart
// خدمة الحصول على معرف الجهاز الفريد
class DeviceIdentificationService {
  static const String _deviceIdKey = 'device_unique_id';

  // الحصول على معرف الجهاز
  static Future<String> getDeviceId() async {
    // محاولة الحصول على المعرف المحفوظ محلياً
    final savedId = await LocalStorageService.getString(_deviceIdKey);
    if (savedId != null && savedId.isNotEmpty) {
      return savedId;
    }

    // إنشاء معرف جديد إذا لم يكن موجوداً
    String deviceId;

    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      deviceId = androidInfo.id; // Android ID
    } else if (Platform.isIOS) {
      final iosInfo = await DeviceInfoPlugin().iosInfo;
      deviceId = iosInfo.identifierForVendor ?? 'ios_${DateTime.now().millisecondsSinceEpoch}';
    } else {
      deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
    }

    // حفظ المعرف محلياً
    await LocalStorageService.setString(_deviceIdKey, deviceId);

    return deviceId;
  }

  // نسخ معرف الجهاز للحافظة
  static Future<void> copyDeviceIdToClipboard() async {
    final deviceId = await getDeviceId();
    await Clipboard.setData(ClipboardData(text: deviceId));
  }
}
```

#### خدمات إرسال بيانات العملاء:
```dart
// خدمة إرسال بيانات الدخول للعملاء
class CustomerCredentialsService {
  // إرسال بيانات الدخول عبر SMS
  static Future<bool> sendCredentialsBySMS({
    required String phoneNumber,
    required String username,
    required String password,
    required String businessName,
  }) async {
    final message = '''
مرحباً، تم إنشاء حساب لك في تطبيق دائن مدين

المنشأة: $businessName
اسم المستخدم: $username
كلمة المرور: $password

يرجى تحميل التطبيق وتسجيل الدخول باستخدام هذه البيانات.
''';

    try {
      // استخدام خدمة SMS (يمكن استخدام Firebase Functions أو خدمة خارجية)
      await SMSService.sendMessage(phoneNumber, message);
      return true;
    } catch (e) {
      return false;
    }
  }

  // إرسال بيانات الدخول عبر WhatsApp
  static Future<bool> sendCredentialsByWhatsApp({
    required String phoneNumber,
    required String username,
    required String password,
    required String businessName,
  }) async {
    final message = '''
مرحباً، تم إنشاء حساب لك في تطبيق دائن مدين

المنشأة: $businessName
اسم المستخدم: $username
كلمة المرور: $password

يرجى تحميل التطبيق وتسجيل الدخول باستخدام هذه البيانات.
''';

    try {
      // فتح WhatsApp مع الرسالة الجاهزة
      final whatsappUrl = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';
      if (await canLaunch(whatsappUrl)) {
        await launch(whatsappUrl);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // توليد كلمة مرور قوية عشوائية
  static String generateSecurePassword({int length = 8}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }
}
```

#### خدمة مراقبة الاشتراكات:
```dart
// خدمة مراقبة حالة الاشتراك
class SubscriptionMonitoringService {
  static Timer? _monitoringTimer;
  static const Duration _checkInterval = Duration(hours: 1);

  // بدء مراقبة الاشتراك
  static void startMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(_checkInterval, (_) => _checkSubscriptionStatus());

    // فحص فوري عند البدء
    _checkSubscriptionStatus();
  }

  // إيقاف المراقبة
  static void stopMonitoring() {
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  // فحص حالة الاشتراك
  static Future<void> _checkSubscriptionStatus() async {
    try {
      final deviceId = await DeviceIdentificationService.getDeviceId();
      final subscription = await SubscriptionService.getSubscriptionByDeviceId(deviceId);

      if (subscription == null || subscription.isExpired) {
        // إنهاء الجلسة وعرض شاشة الاشتراك
        await _handleExpiredSubscription();
      } else if (subscription.needsReminder) {
        // عرض تذكير
        await _showSubscriptionReminder(subscription);
      }

      // تحديث آخر فحص
      await SubscriptionService.updateLastChecked(deviceId);

    } catch (e) {
      // في حالة الخطأ، لا نقوم بإنهاء الجلسة
      print('Error checking subscription status: $e');
    }
  }

  // التعامل مع انتهاء الاشتراك
  static Future<void> _handleExpiredSubscription() async {
    // تسجيل خروج المستخدم
    await AuthController.instance.logout();

    // الانتقال لشاشة انتهاء الاشتراك
    Get.offAllNamed('/subscription-expired');

    // عرض إشعار
    NotificationService.showExpiredSubscriptionNotification();
  }

  // عرض تذكير الاشتراك
  static Future<void> _showSubscriptionReminder(Subscription subscription) async {
    final message = 'ينتهي اشتراكك خلال ${subscription.daysRemaining} أيام. يرجى التجديد لتجنب انقطاع الخدمة.';

    Get.snackbar(
      'تذكير الاشتراك',
      message,
      backgroundColor: subscription.statusColor,
      colorText: Colors.white,
      duration: const Duration(seconds: 10),
      mainButton: TextButton(
        onPressed: () => Get.toNamed('/subscription-info'),
        child: const Text('تجديد الآن', style: TextStyle(color: Colors.white)),
      ),
    );
  }
}
```

---

## 4. تصميم واجهة المستخدم

### 🎨 المبادئ التصميمية

#### التصميم العام:
- **Material Design 3**: اتباع أحدث معايير جوجل للتصميم
- **نظام ألوان موحد**: أبيض وأزرق كحلي مع دعم الوضع الليلي/النهاري
- **التباين العالي**: لضمان سهولة القراءة في جميع الأوضاع
- **الاستجابة**: تصميم متجاوب لجميع أحجام الشاشات
- **دعم الثيمات**: تبديل تلقائي بين الوضع الليلي والنهاري

#### نظام الألوان المحدث:

##### الألوان الأساسية الموحدة:
```dart
class AppColors {
  // الألوان الأساسية
  static const Color primary = Color(0xFF1E3A8A);        // أزرق كحلي داكن
  static const Color primaryDark = Color(0xFF0F172A);    // أزرق كحلي أغمق
  static const Color secondary = Color(0xFF64748B);      // رمادي أزرق
  static const Color accent = Color(0xFF3B82F6);         // أزرق فاتح

  // خلفيات
  static const Color backgroundLight = Color(0xFFFFFFFF); // أبيض
  static const Color backgroundDark = Color(0xFF0F172A);  // أسود مزرق
  static const Color surfaceLight = Color(0xFFF8FAFC);   // رمادي فاتح جداً
  static const Color surfaceDark = Color(0xFF1E293B);    // رمادي داكن

  // النصوص
  static const Color textPrimaryLight = Color(0xFF1E3A8A); // أزرق كحلي
  static const Color textPrimaryDark = Color(0xFFFFFFFF);  // أبيض
  static const Color textSecondaryLight = Color(0xFF64748B); // رمادي
  static const Color textSecondaryDark = Color(0xFF94A3B8); // رمادي فاتح

  // ألوان الحالة (موحدة للوضعين)
  static const Color success = Color(0xFF10B981);        // أخضر
  static const Color warning = Color(0xFFF59E0B);        // برتقالي
  static const Color error = Color(0xFFEF4444);          // أحمر
  static const Color info = Color(0xFF3B82F6);           // أزرق
}
```

##### نظام الثيمات (Light/Dark Mode):
```dart
class AppThemes {
  // الثيم النهاري (Light Theme)
  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,

    // نظام الألوان
    colorScheme: const ColorScheme.light(
      primary: AppColors.primary,
      secondary: AppColors.secondary,
      surface: AppColors.surfaceLight,
      background: AppColors.backgroundLight,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: AppColors.textPrimaryLight,
      onBackground: AppColors.textPrimaryLight,
    ),

    // شريط التطبيق
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.backgroundLight,
      foregroundColor: AppColors.textPrimaryLight,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: AppColors.textPrimaryLight,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),

    // البطاقات
    cardTheme: CardTheme(
      color: AppColors.backgroundLight,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),

    // الأزرار
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
  );

  // الثيم الليلي (Dark Theme)
  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,

    // نظام الألوان
    colorScheme: const ColorScheme.dark(
      primary: AppColors.accent,
      secondary: AppColors.secondary,
      surface: AppColors.surfaceDark,
      background: AppColors.backgroundDark,
      onPrimary: AppColors.backgroundDark,
      onSecondary: Colors.white,
      onSurface: AppColors.textPrimaryDark,
      onBackground: AppColors.textPrimaryDark,
    ),

    // شريط التطبيق
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.backgroundDark,
      foregroundColor: AppColors.textPrimaryDark,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: AppColors.textPrimaryDark,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),

    // البطاقات
    cardTheme: CardTheme(
      color: AppColors.surfaceDark,
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    ),

    // الأزرار
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.accent,
        foregroundColor: AppColors.backgroundDark,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    ),
  );
}
```

#### إدارة الثيمات والتبديل:
```dart
class ThemeController extends GetxController {
  // الحالة الحالية للثيم
  var isDarkMode = false.obs;

  // تبديل الثيم
  void toggleTheme() {
    isDarkMode.value = !isDarkMode.value;
    Get.changeTheme(isDarkMode.value ? AppThemes.darkTheme : AppThemes.lightTheme);
    _saveThemePreference();
  }

  // تحميل تفضيلات الثيم المحفوظة
  void loadThemePreference() async {
    final savedTheme = await LocalStorage.getBool('isDarkMode') ?? false;
    isDarkMode.value = savedTheme;
    Get.changeTheme(isDarkMode.value ? AppThemes.darkTheme : AppThemes.lightTheme);
  }

  // حفظ تفضيلات الثيم
  void _saveThemePreference() async {
    await LocalStorage.setBool('isDarkMode', isDarkMode.value);
  }

  // تبديل تلقائي حسب النظام
  void followSystemTheme() {
    final brightness = WidgetsBinding.instance.window.platformBrightness;
    isDarkMode.value = brightness == Brightness.dark;
    Get.changeTheme(isDarkMode.value ? AppThemes.darkTheme : AppThemes.lightTheme);
  }
}
```

### 🎨 نظام إدارة التصميم المركزي

#### الهدف من النظام المركزي:
- **سهولة الصيانة**: تغيير واحد يؤثر على التطبيق بأكمله
- **الاتساق**: ضمان توحيد التصميم عبر جميع الشاشات
- **الأداء**: استخدام const values لتحسين الأداء
- **قابلية التوسع**: إضافة عناصر جديدة بسهولة

#### 1. ملف الألوان المركزي (app_colors.dart):
```dart
import 'package:flutter/material.dart';

class AppColors {
  // منع إنشاء instance من الكلاس
  AppColors._();

  // ===== الألوان الأساسية =====
  static const Color primary = Color(0xFF1E3A8A);           // أزرق كحلي داكن
  static const Color primaryLight = Color(0xFF3B82F6);      // أزرق كحلي فاتح
  static const Color primaryDark = Color(0xFF0F172A);       // أزرق كحلي أغمق
  static const Color secondary = Color(0xFF64748B);         // رمادي أزرق
  static const Color accent = Color(0xFF3B82F6);            // أزرق فاتح

  // ===== ألوان الخلفيات =====
  static const Color backgroundLight = Color(0xFFFFFFFF);   // أبيض
  static const Color backgroundDark = Color(0xFF0F172A);    // أسود مزرق
  static const Color surfaceLight = Color(0xFFF8FAFC);     // رمادي فاتح جداً
  static const Color surfaceDark = Color(0xFF1E293B);      // رمادي داكن
  static const Color cardLight = Color(0xFFFFFFFF);        // بطاقات فاتحة
  static const Color cardDark = Color(0xFF1E293B);         // بطاقات داكنة

  // ===== ألوان النصوص =====
  static const Color textPrimaryLight = Color(0xFF1E3A8A); // نص أساسي فاتح
  static const Color textPrimaryDark = Color(0xFFFFFFFF);  // نص أساسي داكن
  static const Color textSecondaryLight = Color(0xFF64748B); // نص ثانوي فاتح
  static const Color textSecondaryDark = Color(0xFF94A3B8); // نص ثانوي داكن
  static const Color textHintLight = Color(0xFF9CA3AF);    // نص تلميح فاتح
  static const Color textHintDark = Color(0xFF6B7280);     // نص تلميح داكن

  // ===== ألوان الحالة =====
  static const Color success = Color(0xFF10B981);          // أخضر - نجاح
  static const Color successLight = Color(0xFF34D399);     // أخضر فاتح
  static const Color successDark = Color(0xFF059669);      // أخضر داكن

  static const Color warning = Color(0xFFF59E0B);          // برتقالي - تحذير
  static const Color warningLight = Color(0xFFFBBF24);     // برتقالي فاتح
  static const Color warningDark = Color(0xFFD97706);      // برتقالي داكن

  static const Color error = Color(0xFFEF4444);            // أحمر - خطأ
  static const Color errorLight = Color(0xFFF87171);       // أحمر فاتح
  static const Color errorDark = Color(0xFFDC2626);        // أحمر داكن

  static const Color info = Color(0xFF3B82F6);             // أزرق - معلومات
  static const Color infoLight = Color(0xFF60A5FA);        // أزرق فاتح
  static const Color infoDark = Color(0xFF2563EB);         // أزرق داكن

  // ===== ألوان الحدود والفواصل =====
  static const Color borderLight = Color(0xFFE5E7EB);      // حدود فاتحة
  static const Color borderDark = Color(0xFF374151);       // حدود داكنة
  static const Color dividerLight = Color(0xFFF3F4F6);     // فاصل فاتح
  static const Color dividerDark = Color(0xFF4B5563);      // فاصل داكن

  // ===== ألوان خاصة بالتطبيق =====
  static const Color debt = Color(0xFFEF4444);             // لون الديون
  static const Color payment = Color(0xFF10B981);          // لون المدفوعات
  static const Color customer = Color(0xFF3B82F6);         // لون العملاء
  static const Color report = Color(0xFF8B5CF6);           // لون التقارير

  // ===== ألوان الشفافية =====
  static const Color overlay = Color(0x80000000);          // طبقة شفافة
  static const Color shadow = Color(0x1A000000);           // ظل

  // ===== دوال مساعدة =====

  // الحصول على لون النص المناسب للخلفية
  static Color getTextColor(Color backgroundColor) {
    return backgroundColor.computeLuminance() > 0.5
        ? textPrimaryLight
        : textPrimaryDark;
  }

  // الحصول على لون الحالة مع الشفافية
  static Color getStatusColorWithOpacity(String status, double opacity) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'paid':
        return success.withOpacity(opacity);
      case 'warning':
      case 'pending':
        return warning.withOpacity(opacity);
      case 'error':
      case 'failed':
        return error.withOpacity(opacity);
      case 'info':
      default:
        return info.withOpacity(opacity);
    }
  }

  // الحصول على تدرج لوني
  static LinearGradient getPrimaryGradient() {
    return const LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [primary, primaryLight],
    );
  }

  // الحصول على تدرج لوني للحالة
  static LinearGradient getStatusGradient(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return LinearGradient(
          colors: [success, successLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'warning':
        return LinearGradient(
          colors: [warning, warningLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case 'error':
        return LinearGradient(
          colors: [error, errorLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return LinearGradient(
          colors: [info, infoLight],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
    }
  }
}
```

#### 2. ملف الأيقونات المركزي (app_icons.dart):
```dart
import 'package:flutter/material.dart';

class AppIcons {
  // منع إنشاء instance من الكلاس
  AppIcons._();

  // ===== أيقونات التنقل =====
  static const IconData home = Icons.home_rounded;
  static const IconData customers = Icons.people_rounded;
  static const IconData debts = Icons.receipt_long_rounded;
  static const IconData payments = Icons.payment_rounded;
  static const IconData reports = Icons.analytics_rounded;
  static const IconData settings = Icons.settings_rounded;
  static const IconData profile = Icons.person_rounded;

  // ===== أيقونات الإجراءات =====
  static const IconData add = Icons.add_rounded;
  static const IconData edit = Icons.edit_rounded;
  static const IconData delete = Icons.delete_rounded;
  static const IconData save = Icons.save_rounded;
  static const IconData cancel = Icons.cancel_rounded;
  static const IconData confirm = Icons.check_circle_rounded;
  static const IconData search = Icons.search_rounded;
  static const IconData filter = Icons.filter_list_rounded;
  static const IconData sort = Icons.sort_rounded;
  static const IconData refresh = Icons.refresh_rounded;

  // ===== أيقونات الحالة =====
  static const IconData success = Icons.check_circle_rounded;
  static const IconData warning = Icons.warning_rounded;
  static const IconData error = Icons.error_rounded;
  static const IconData info = Icons.info_rounded;
  static const IconData pending = Icons.schedule_rounded;
  static const IconData loading = Icons.hourglass_empty_rounded;

  // ===== أيقونات المالية =====
  static const IconData money = Icons.attach_money_rounded;
  static const IconData wallet = Icons.account_balance_wallet_rounded;
  static const IconData credit = Icons.credit_card_rounded;
  static const IconData cash = Icons.money_rounded;
  static const IconData bank = Icons.account_balance_rounded;
  static const IconData receipt = Icons.receipt_rounded;

  // ===== أيقونات التواصل =====
  static const IconData phone = Icons.phone_rounded;
  static const IconData email = Icons.email_rounded;
  static const IconData message = Icons.message_rounded;
  static const IconData whatsapp = Icons.chat_rounded;
  static const IconData notification = Icons.notifications_rounded;

  // ===== أيقونات الأمان =====
  static const IconData lock = Icons.lock_rounded;
  static const IconData unlock = Icons.lock_open_rounded;
  static const IconData fingerprint = Icons.fingerprint_rounded;
  static const IconData faceId = Icons.face_rounded;
  static const IconData security = Icons.security_rounded;

  // ===== أيقونات الثيم =====
  static const IconData lightMode = Icons.light_mode_rounded;
  static const IconData darkMode = Icons.dark_mode_rounded;
  static const IconData autoMode = Icons.brightness_auto_rounded;

  // ===== أيقونات الملفات =====
  static const IconData pdf = Icons.picture_as_pdf_rounded;
  static const IconData excel = Icons.table_chart_rounded;
  static const IconData image = Icons.image_rounded;
  static const IconData download = Icons.download_rounded;
  static const IconData upload = Icons.upload_rounded;
  static const IconData share = Icons.share_rounded;

  // ===== أيقونات التطبيق =====
  static const IconData logo = Icons.account_balance_wallet_rounded;
  static const IconData subscription = Icons.card_membership_rounded;
  static const IconData trial = Icons.schedule_rounded;
  static const IconData premium = Icons.star_rounded;

  // ===== دوال مساعدة =====

  // الحصول على أيقونة الحالة
  static IconData getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'paid':
      case 'completed':
        return success;
      case 'warning':
      case 'pending':
        return warning;
      case 'error':
      case 'failed':
      case 'cancelled':
        return error;
      case 'info':
      case 'active':
        return info;
      default:
        return info;
    }
  }

  // الحصول على أيقونة نوع الدفع
  static IconData getPaymentMethodIcon(String method) {
    switch (method.toLowerCase()) {
      case 'cash':
      case 'نقدي':
        return cash;
      case 'card':
      case 'بطاقة':
        return credit;
      case 'bank':
      case 'بنكي':
        return bank;
      default:
        return money;
    }
  }

  // إنشاء أيقونة مع لون مخصص
  static Widget createColoredIcon(
    IconData icon, {
    Color? color,
    double? size,
    String? semanticLabel,
  }) {
    return Icon(
      icon,
      color: color,
      size: size,
      semanticLabel: semanticLabel,
    );
  }

  // إنشاء أيقونة مع خلفية دائرية
  static Widget createCircularIcon(
    IconData icon, {
    Color? backgroundColor,
    Color? iconColor,
    double? radius,
    double? iconSize,
  }) {
    return CircleAvatar(
      radius: radius ?? 20,
      backgroundColor: backgroundColor ?? AppColors.primary,
      child: Icon(
        icon,
        color: iconColor ?? Colors.white,
        size: iconSize ?? 20,
      ),
    );
  }
}
```

#### 3. ملف أنماط النصوص المركزي (app_text_styles.dart):
```dart
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'app_colors.dart';

class AppTextStyles {
  // منع إنشاء instance من الكلاس
  AppTextStyles._();

  // ===== الخط الأساسي =====
  static String get fontFamily => GoogleFonts.cairo().fontFamily!;

  // ===== أحجام الخطوط =====
  static const double fontSizeXSmall = 10.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;
  static const double fontSizeDisplay = 32.0;

  // ===== أوزان الخطوط =====
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;

  // ===== أنماط العناوين =====
  static TextStyle get displayLarge => GoogleFonts.cairo(
    fontSize: fontSizeDisplay,
    fontWeight: fontWeightBold,
    color: AppColors.textPrimaryLight,
    height: 1.2,
  );

  static TextStyle get headlineMedium => GoogleFonts.cairo(
    fontSize: fontSizeXLarge,
    fontWeight: fontWeightMedium,
    color: AppColors.textPrimaryLight,
    height: 1.4,
  );

  // ===== أنماط النصوص الأساسية =====
  static TextStyle get bodyLarge => GoogleFonts.cairo(
    fontSize: fontSizeLarge,
    fontWeight: fontWeightRegular,
    color: AppColors.textPrimaryLight,
    height: 1.5,
  );

  static TextStyle get bodyMedium => GoogleFonts.cairo(
    fontSize: fontSizeMedium,
    fontWeight: fontWeightRegular,
    color: AppColors.textPrimaryLight,
    height: 1.5,
  );

  // ===== أنماط خاصة بالتطبيق =====

  // نمط أرقام المبالغ المالية
  static TextStyle get currency => GoogleFonts.cairo(
    fontSize: fontSizeXLarge,
    fontWeight: fontWeightBold,
    color: AppColors.primary,
    height: 1.2,
  );

  // نمط نص الأزرار
  static TextStyle get button => GoogleFonts.cairo(
    fontSize: fontSizeMedium,
    fontWeight: fontWeightSemiBold,
    color: Colors.white,
    height: 1.2,
  );

  // ===== دوال مساعدة =====

  // الحصول على نمط حسب الحالة
  static TextStyle getStatusTextStyle(String status) {
    Color color;
    switch (status.toLowerCase()) {
      case 'success':
      case 'paid':
        color = AppColors.success;
        break;
      case 'warning':
      case 'pending':
        color = AppColors.warning;
        break;
      case 'error':
      case 'failed':
        color = AppColors.error;
        break;
      default:
        color = AppColors.info;
    }

    return bodyMedium.copyWith(
      color: color,
      fontWeight: fontWeightSemiBold,
    );
  }

  // الحصول على نمط للوضع الداكن
  static TextStyle getDarkModeStyle(TextStyle lightStyle) {
    Color darkColor;
    if (lightStyle.color == AppColors.textPrimaryLight) {
      darkColor = AppColors.textPrimaryDark;
    } else if (lightStyle.color == AppColors.textSecondaryLight) {
      darkColor = AppColors.textSecondaryDark;
    } else {
      darkColor = lightStyle.color ?? AppColors.textPrimaryDark;
    }

    return lightStyle.copyWith(color: darkColor);
  }
}
```

#### 4. ملف التزيينات المركزي (app_decorations.dart):
```dart
import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppDecorations {
  // منع إنشاء instance من الكلاس
  AppDecorations._();

  // ===== أنصاف الأقطار =====
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusCircular = 50.0;

  // ===== المسافات =====
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;

  // ===== الارتفاعات (Elevation) =====
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;

  // ===== تزيينات البطاقات =====
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: AppColors.cardLight,
    borderRadius: BorderRadius.circular(radiusLarge),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadow,
        blurRadius: elevationSmall,
        offset: const Offset(0, 1),
      ),
    ],
  );

  // ===== تزيينات الأزرار =====
  static BoxDecoration get primaryButtonDecoration => BoxDecoration(
    color: AppColors.primary,
    borderRadius: BorderRadius.circular(radiusMedium),
    boxShadow: [
      BoxShadow(
        color: AppColors.primary.withOpacity(0.3),
        blurRadius: elevationSmall,
        offset: const Offset(0, 2),
      ),
    ],
  );

  // ===== تزيينات حقول الإدخال =====
  static InputDecoration getInputDecoration({
    required String label,
    String? hint,
    IconData? prefixIcon,
    bool isDark = false,
  }) {
    return InputDecoration(
      labelText: label,
      hintText: hint,
      prefixIcon: prefixIcon != null
          ? Icon(
              prefixIcon,
              color: isDark ? AppColors.textSecondaryDark : AppColors.primary,
            )
          : null,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        borderSide: BorderSide(
          color: isDark ? AppColors.borderDark : AppColors.borderLight,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLarge),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      filled: true,
      fillColor: isDark ? AppColors.surfaceDark : AppColors.surfaceLight,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: spacingMedium,
        vertical: spacingMedium,
      ),
    );
  }

  // ===== تزيينات الحالة =====
  static BoxDecoration getStatusDecoration(String status) {
    Color color = AppColors.getStatusColorWithOpacity(status, 0.1);
    Color borderColor = AppColors.getStatusColorWithOpacity(status, 1.0);

    return BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(radiusSmall),
      border: Border.all(color: borderColor, width: 1),
    );
  }

  // ===== دوال مساعدة =====

  // إنشاء تزيين مخصص للبطاقة
  static BoxDecoration createCardDecoration({
    Color? backgroundColor,
    double? borderRadius,
    Color? shadowColor,
    double? elevation,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? AppColors.cardLight,
      borderRadius: BorderRadius.circular(borderRadius ?? radiusLarge),
      boxShadow: elevation != null && elevation > 0
          ? [
              BoxShadow(
                color: shadowColor ?? AppColors.shadow,
                blurRadius: elevation,
                offset: Offset(0, elevation / 2),
              ),
            ]
          : null,
    );
  }
}
```

#### بنية المشروع (Project Structure):
```
lib/
├── app/
│   ├── bindings/          # ربط التبعيات
│   ├── controllers/       # تحكم في الحالة
│   ├── data/             # نماذج البيانات والخدمات
│   ├── modules/          # الوحدات الرئيسية
│   ├── routes/           # التنقل والمسارات
│   └── widgets/          # العناصر المشتركة
├── core/
│   ├── constants/        # الثوابت والتصميم المركزي
│   │   ├── app_colors.dart      # جميع ألوان التطبيق
│   │   ├── app_icons.dart       # جميع أيقونات التطبيق
│   │   ├── app_text_styles.dart # جميع أنماط النصوص
│   │   ├── app_decorations.dart # جميع التزيينات والحدود
│   │   ├── app_strings.dart     # جميع النصوص الثابتة
│   │   └── app_constants.dart   # الثوابت العامة
│   ├── themes/          # الثيمات والتصاميم
│   │   ├── app_themes.dart      # الثيمات الرئيسية
│   │   ├── light_theme.dart     # الثيم النهاري
│   │   └── dark_theme.dart      # الثيم الليلي
│   ├── utils/           # الأدوات المساعدة
│   │   ├── validators.dart      # التحقق من البيانات
│   │   ├── formatters.dart      # تنسيق البيانات
│   │   ├── helpers.dart         # دوال مساعدة
│   │   └── extensions.dart      # إضافات للكلاسات الموجودة
│   └── services/        # الخدمات الأساسية
│       ├── theme_service.dart   # خدمة إدارة الثيمات
│       ├── storage_service.dart # خدمة التخزين المحلي
│       └── device_service.dart  # خدمة معلومات الجهاز
└── main.dart            # نقطة البداية
```

#### 5. أمثلة عملية لاستخدام النظام المركزي:

##### مثال 1: إنشاء بطاقة عميل باستخدام النظام المركزي:
```dart
import '../core/constants/app_colors.dart';
import '../core/constants/app_icons.dart';
import '../core/constants/app_text_styles.dart';
import '../core/constants/app_decorations.dart';

class CustomerCard extends StatelessWidget {
  final Customer customer;
  final VoidCallback? onTap;

  const CustomerCard({
    Key? key,
    required this.customer,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: isDark
          ? AppDecorations.cardDecorationDark
          : AppDecorations.cardDecoration,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDecorations.radiusLarge),
        child: Padding(
          padding: const EdgeInsets.all(AppDecorations.spacingMedium),
          child: Row(
            children: [
              // أيقونة العميل
              AppIcons.createCircularIcon(
                AppIcons.customers,
                backgroundColor: AppColors.primary,
                iconColor: Colors.white,
                radius: 24,
              ),
              const SizedBox(width: AppDecorations.spacingMedium),

              // معلومات العميل
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم العميل
                    Text(
                      customer.name,
                      style: isDark
                          ? AppTextStyles.getDarkModeStyle(AppTextStyles.headlineMedium)
                          : AppTextStyles.headlineMedium,
                    ),
                    const SizedBox(height: 4),

                    // رقم الهاتف
                    if (customer.phone != null)
                      Row(
                        children: [
                          Icon(
                            AppIcons.phone,
                            size: 16,
                            color: isDark
                                ? AppColors.textSecondaryDark
                                : AppColors.textSecondaryLight,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            customer.phone!,
                            style: isDark
                                ? AppTextStyles.getDarkModeStyle(AppTextStyles.bodyMedium)
                                : AppTextStyles.bodyMedium,
                          ),
                        ],
                      ),
                  ],
                ),
              ),

              // المبلغ المستحق
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${customer.totalDebt.toStringAsFixed(2)} ر.س',
                    style: AppTextStyles.currency.copyWith(
                      color: customer.totalDebt > 0
                          ? AppColors.debt
                          : AppColors.success,
                    ),
                  ),
                  const SizedBox(height: 4),

                  // حالة العميل
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDecorations.spacingSmall,
                      vertical: 2,
                    ),
                    decoration: AppDecorations.getStatusDecoration(
                      customer.totalDebt > 0 ? 'warning' : 'success',
                    ),
                    child: Text(
                      customer.totalDebt > 0 ? 'له ديون' : 'مسدد',
                      style: AppTextStyles.getStatusTextStyle(
                        customer.totalDebt > 0 ? 'warning' : 'success',
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

##### مثال 2: إنشاء زر مخصص باستخدام النظام المركزي:
```dart
class CustomElevatedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final String buttonType; // 'primary', 'secondary', 'success', 'warning', 'error'

  const CustomElevatedButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.buttonType = 'primary',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color textColor = Colors.white;

    // تحديد الألوان حسب نوع الزر
    switch (buttonType) {
      case 'success':
        backgroundColor = AppColors.success;
        break;
      case 'warning':
        backgroundColor = AppColors.warning;
        break;
      case 'error':
        backgroundColor = AppColors.error;
        break;
      case 'secondary':
        backgroundColor = AppColors.secondary;
        break;
      default:
        backgroundColor = AppColors.primary;
    }

    return Container(
      decoration: AppDecorations.primaryButtonDecoration.copyWith(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: AppDecorations.elevationSmall,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: BorderRadius.circular(AppDecorations.radiusMedium),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDecorations.spacingLarge,
              vertical: AppDecorations.spacingMedium,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(textColor),
                    ),
                  )
                else if (icon != null)
                  Icon(icon, color: textColor, size: 18),

                if ((isLoading || icon != null) && text.isNotEmpty)
                  const SizedBox(width: AppDecorations.spacingSmall),

                if (text.isNotEmpty)
                  Text(
                    text,
                    style: AppTextStyles.button.copyWith(color: textColor),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
```

##### مثال 3: إنشاء حقل إدخال باستخدام النظام المركزي:
```dart
class CustomFormField extends StatelessWidget {
  final String label;
  final String? hint;
  final IconData? prefixIcon;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;

  const CustomFormField({
    Key? key,
    required this.label,
    this.hint,
    this.prefixIcon,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      obscureText: obscureText,
      style: isDark
          ? AppTextStyles.getDarkModeStyle(AppTextStyles.bodyLarge)
          : AppTextStyles.bodyLarge,
      decoration: AppDecorations.getInputDecoration(
        label: label,
        hint: hint,
        prefixIcon: prefixIcon,
        isDark: isDark,
      ),
    );
  }
}
```

##### مثال 4: كيفية تغيير الثيم الكامل من مكان واحد:
```dart
// في ملف app_colors.dart - لتغيير اللون الأساسي للتطبيق بأكمله
class AppColors {
  // تغيير هذا اللون سيؤثر على التطبيق بأكمله
  static const Color primary = Color(0xFF2563EB); // تغيير من الأزرق الكحلي إلى أزرق أفتح

  // باقي الألوان...
}

// في ملف app_text_styles.dart - لتغيير الخط الأساسي
class AppTextStyles {
  // تغيير هذا الخط سيؤثر على جميع النصوص
  static String get fontFamily => GoogleFonts.tajawal().fontFamily!; // تغيير من Cairo إلى Tajawal

  // باقي الأنماط...
}

// في ملف app_decorations.dart - لتغيير نصف القطر الأساسي
class AppDecorations {
  // تغيير هذا الرقم سيؤثر على جميع البطاقات والأزرار
  static const double radiusLarge = 16.0; // تغيير من 12.0 إلى 16.0

  // باقي التزيينات...
}
```

#### 6. فوائد النظام المركزي:

##### سهولة الصيانة:
- **تغيير واحد يؤثر على التطبيق بأكمله**: تغيير لون واحد في `AppColors` يحدث جميع الشاشات
- **عدم الحاجة للبحث في ملفات متعددة**: جميع الألوان في مكان واحد
- **تقليل الأخطاء**: لا يمكن نسيان تحديث لون في شاشة معينة

##### الاتساق في التصميم:
- **ألوان موحدة**: جميع الشاشات تستخدم نفس الألوان
- **أنماط نصوص متسقة**: جميع العناوين لها نفس النمط
- **مسافات منتظمة**: جميع العناصر تستخدم نفس المسافات

##### تحسين الأداء:
- **استخدام const values**: جميع الألوان والقيم ثابتة
- **عدم إعادة إنشاء الكائنات**: الألوان والأنماط تُنشأ مرة واحدة
- **ذاكرة أقل**: لا توجد نسخ متعددة من نفس القيم

#### 7. إرشادات الاستخدام والصيانة:

##### كيفية إضافة لون جديد:
```dart
// في ملف app_colors.dart
class AppColors {
  // الألوان الموجودة...

  // إضافة لون جديد
  static const Color newFeature = Color(0xFF9333EA); // بنفسجي للميزة الجديدة

  // إضافة تدرج جديد
  static LinearGradient getNewFeatureGradient() {
    return const LinearGradient(
      colors: [newFeature, Color(0xFFA855F7)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
}

// الاستخدام في أي مكان في التطبيق
Container(
  color: AppColors.newFeature,
  child: Text('ميزة جديدة'),
)
```

##### كيفية إضافة أيقونة جديدة:
```dart
// في ملف app_icons.dart
class AppIcons {
  // الأيقونات الموجودة...

  // إضافة أيقونة جديدة
  static const IconData newFeature = Icons.star_border_rounded;

  // إضافة دالة مساعدة جديدة
  static IconData getFeatureIcon(String featureType) {
    switch (featureType) {
      case 'new':
        return newFeature;
      case 'premium':
        return premium;
      default:
        return info;
    }
  }
}

// الاستخدام
Icon(AppIcons.newFeature, color: AppColors.newFeature)
```

##### كيفية إضافة نمط نص جديد:
```dart
// في ملف app_text_styles.dart
class AppTextStyles {
  // الأنماط الموجودة...

  // إضافة نمط جديد
  static TextStyle get specialOffer => GoogleFonts.cairo(
    fontSize: fontSizeXLarge,
    fontWeight: fontWeightBold,
    color: AppColors.newFeature,
    height: 1.2,
    decoration: TextDecoration.underline,
  );
}

// الاستخدام
Text(
  'عرض خاص!',
  style: AppTextStyles.specialOffer,
)
```

##### كيفية تحديث الثيم الكامل:
```dart
// لتغيير اللون الأساسي للتطبيق بأكمله
// في ملف app_colors.dart
static const Color primary = Color(0xFF059669); // تغيير إلى أخضر

// لتغيير الخط الأساسي للتطبيق بأكمله
// في ملف app_text_styles.dart
static String get fontFamily => GoogleFonts.amiri().fontFamily!; // تغيير إلى أميري

// لتغيير نصف القطر الأساسي للتطبيق بأكمله
// في ملف app_decorations.dart
static const double radiusLarge = 20.0; // تغيير إلى 20 بكسل
```

##### قائمة التحقق للصيانة:
- [ ] **عند إضافة لون جديد**: إضافته في `AppColors` وليس في الملفات الفردية
- [ ] **عند إضافة أيقونة جديدة**: إضافتها في `AppIcons` مع دالة مساعدة إذا لزم الأمر
- [ ] **عند إضافة نمط نص جديد**: إضافته في `AppTextStyles` مع دعم الوضع الداكن
- [ ] **عند تغيير التصميم**: التأكد من التحديث في الملفات المركزية فقط
- [ ] **اختبار الثيمات**: التأكد من عمل التغييرات في الوضع الليلي والنهاري
- [ ] **مراجعة الاتساق**: التأكد من توحيد التصميم عبر جميع الشاشات

##### نصائح للمطورين:
1. **استخدم دائماً الملفات المركزية**: لا تضع ألوان أو أنماط مباشرة في الويدجت
2. **اتبع التسمية الواضحة**: استخدم أسماء وصفية للألوان والأنماط
3. **اختبر في الوضعين**: تأكد من عمل التصميم في الوضع الليلي والنهاري
4. **استخدم const**: لتحسين الأداء وتوفير الذاكرة
5. **وثق التغييرات**: اكتب تعليقات واضحة عند إضافة عناصر جديدة

#### نظام إدارة الحالة (State Management):
```dart
// استخدام GetX لإدارة الحالة
class HomeController extends GetxController {
  // البيانات التفاعلية
  var isLoading = false.obs;
  var customersList = <Customer>[].obs;
  var totalDebts = 0.0.obs;
  var totalPayments = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  // تحميل البيانات الأولية
  void loadInitialData() async {
    isLoading.value = true;
    try {
      // تحميل البيانات (سيتم ربطها بقاعدة البيانات لاحقاً)
      await loadCustomers();
      await calculateTotals();
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث البيانات
  void refreshData() async {
    await loadInitialData();
  }
}
```

#### نظام التنقل (Navigation System):
```dart
class AppRoutes {
  // مسارات التطبيق
  static const String splash = '/splash';
  static const String login = '/login';
  static const String home = '/home';
  static const String customers = '/customers';
  static const String debts = '/debts';
  static const String payments = '/payments';
  static const String reports = '/reports';
  static const String settings = '/settings';
  static const String subscriptionExpired = '/subscription-expired';

  // صفحات التطبيق
  static List<GetPage> pages = [
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: login,
      page: () => const LoginScreen(),
      binding: AuthBinding(),
    ),
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      binding: HomeBinding(),
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: customers,
      page: () => const CustomersScreen(),
      binding: CustomersBinding(),
      middlewares: [AuthMiddleware()],
    ),
    // باقي الصفحات...
  ];
}

// Middleware للتحقق من المصادقة
class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final authController = Get.find<AuthController>();
    if (!authController.isLoggedIn.value) {
      return const RouteSettings(name: AppRoutes.login);
    }
    return null;
  }
}
```

#### نظام الحقن (Dependency Injection):
```dart
// ربط التبعيات الأساسية
class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // الخدمات الأساسية
    Get.put<ThemeController>(ThemeController(), permanent: true);
    Get.put<AuthController>(AuthController(), permanent: true);
    Get.put<LocalStorageService>(LocalStorageService(), permanent: true);

    // خدمات البيانات (ستتم إضافتها لاحقاً)
    // Get.put<DatabaseService>(DatabaseService(), permanent: true);
  }
}

// ربط تبعيات الصفحة الرئيسية
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<CustomersController>(() => CustomersController());
    Get.lazyPut<DebtsController>(() => DebtsController());
  }
}
```



### 📱 الشاشات الرئيسية

#### 1. شاشة تسجيل الدخول:
**التصميم والألوان:**
```dart
class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار التطبيق
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.account_balance_wallet,
                  size: 60,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              // عنوان التطبيق
              Text(
                'دائن مدين',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
              ),
              const SizedBox(height: 8),

              Text(
                'نظام إدارة الديون الاحترافي',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 48),

              // حقول الإدخال
              _buildInputField(
                context,
                label: 'اسم المستخدم',
                icon: Icons.person,
              ),
              const SizedBox(height: 16),

              _buildInputField(
                context,
                label: 'كلمة المرور',
                icon: Icons.lock,
                isPassword: true,
              ),
              const SizedBox(height: 32),

              // زر تسجيل الدخول
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () => _handleLogin(),
                  child: const Text(
                    'تسجيل الدخول',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputField(BuildContext context, {
    required String label,
    required IconData icon,
    bool isPassword = false,
  }) {
    return TextFormField(
      obscureText: isPassword,
      style: TextStyle(color: Theme.of(context).colorScheme.onBackground),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Theme.of(context).colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
        ),
      ),
    );
  }
}
```

**المكونات:**
- **شعار التطبيق**: أيقونة محفظة بخلفية زرقاء كحلية
- **حقول الإدخال**: بتصميم Material Design 3 مع الألوان الجديدة
- **خيارات المصادقة**: بصمة، Face ID (بألوان متناسقة)
- **أزرار العمل**: بالألوان الأساسية الجديدة
- **تبديل الثيم**: أيقونة في الزاوية العلوية للتبديل بين الوضع الليلي/النهاري

#### 2. لوحة تحكم مالك المنشأة:
**التصميم والألوان:**
```dart
class BusinessOwnerDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: _buildAppBar(context),
      drawer: _buildDrawer(context),
      body: RefreshIndicator(
        onRefresh: () => Get.find<HomeController>().refreshData(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بانر حالة الاشتراك
              _buildSubscriptionBanner(context),
              const SizedBox(height: 16),

              // بطاقة الترحيب
              _buildWelcomeCard(context),
              const SizedBox(height: 24),

              // الإحصائيات السريعة
              _buildQuickStats(context),
              const SizedBox(height: 24),

              // الإجراءات السريعة
              _buildQuickActions(context),
              const SizedBox(height: 24),

              // الأنشطة الأخيرة
              _buildRecentActivities(context),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text('لوحة التحكم'),
      actions: [
        // تبديل الثيم
        IconButton(
          icon: Icon(
            Get.find<ThemeController>().isDarkMode.value
                ? Icons.light_mode
                : Icons.dark_mode,
          ),
          onPressed: () => Get.find<ThemeController>().toggleTheme(),
        ),
        // الإشعارات
        IconButton(
          icon: const Icon(Icons.notifications),
          onPressed: () => _showNotifications(),
        ),
        // الإعدادات
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => Get.toNamed('/settings'),
        ),
      ],
    );
  }

  Widget _buildSubscriptionBanner(BuildContext context) {
    return Obx(() {
      final subscription = Get.find<SubscriptionController>().currentSubscription.value;
      if (subscription == null) return const SizedBox.shrink();

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: subscription.statusColor.withOpacity(0.1),
          border: Border.all(color: subscription.statusColor),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              subscription.isTrial ? Icons.schedule : Icons.verified,
              color: subscription.statusColor,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subscription.statusText,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: subscription.statusColor,
                    ),
                  ),
                  if (subscription.daysRemaining > 0)
                    Text(
                      'متبقي ${subscription.daysRemaining} أيام',
                      style: TextStyle(color: subscription.statusColor),
                    ),
                ],
              ),
            ),
            if (subscription.needsReminder)
              ElevatedButton(
                onPressed: () => Get.toNamed('/subscription-info'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: subscription.statusColor,
                ),
                child: const Text('تجديد'),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildQuickStats(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildStatCard(
          context,
          title: 'العملاء',
          value: '150',
          icon: Icons.people,
          color: AppColors.info,
        )),
        const SizedBox(width: 12),
        Expanded(child: _buildStatCard(
          context,
          title: 'الديون المعلقة',
          value: '25,000 ر.س',
          icon: Icons.pending_actions,
          color: AppColors.warning,
        )),
        const SizedBox(width: 12),
        Expanded(child: _buildStatCard(
          context,
          title: 'المدفوعات اليوم',
          value: '5,000 ر.س',
          icon: Icons.payments,
          color: AppColors.success,
        )),
      ],
    );
  }

  Widget _buildStatCard(BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

**المكونات المحدثة:**
- **شريط التطبيق**: مع زر تبديل الثيم وألوان متناسقة
- **بانر الاشتراك**: بألوان ديناميكية حسب الحالة
- **بطاقات الإحصائيات**: بتصميم Material Design 3
- **الإجراءات السريعة**: بألوان مميزة لكل إجراء
- **القائمة الجانبية**: بالألوان الجديدة والأيقونات المحدثة

#### 3. شاشة إدارة العملاء:
**المكونات:**
- **شريط البحث**: بحث بالاسم أو الهاتف
- **أزرار الفلترة**: حسب الحالة، تاريخ التسجيل، المبلغ المستحق
- **قائمة العملاء**: بطاقات تحتوي على:
  - صورة العميل (أو أيقونة افتراضية)
  - اسم العميل ورقم الهاتف
  - إجمالي الديون والرصيد الحالي
  - تاريخ آخر معاملة
  - أيقونات الإجراءات السريعة
- **زر الإضافة العائم**: لإضافة عميل جديد

**التفاعلات:**
- **النقر على العميل**: عرض تفاصيل العميل
- **السحب لليسار**: إجراءات سريعة (تعديل، حذف)
- **النقر المطول**: تحديد متعدد للعملاء

#### 4. شاشة إدارة الديون:
**المكونات:**
- **تبويبات علوية**: الديون المعلقة، المدفوعة، جميع الديون
- **إحصائيات سريعة**: إجمالي الديون، المعلق، المدفوع
- **قائمة الديون**: بطاقات تحتوي على:
  - اسم العميل وصورته
  - مبلغ الدين ووصفه
  - تاريخ الإنشاء وحالة الدفع
  - شريط تقدم للديون الجزئية
  - أزرار الإجراءات (تحديث، طباعة، تذكير)

**التفاعلات:**
- **النقر على الدين**: عرض تفاصيل كاملة
- **النقر على "تحديث الحالة"**: تغيير حالة الدين
- **النقر على "طباعة"**: إنشاء فاتورة PDF

#### 5. لوحة تحكم العميل:
**المكونات:**
- **بطاقة الترحيب**: ترحيب شخصي مع معلومات الحساب
- **بانر إعلاني**: معلومات عن الخدمات أو العروض
- **الخدمات السريعة**: شبكة 2x2 تحتوي على:
  - المدفوعات (أخضر)
  - الديون (برتقالي)
  - التقارير (أزرق)
  - الطلبات (بنفسجي)
- **النظرة المالية**: إجمالي الديون والمدفوعات

**التفاعلات:**
- **النقر على الخدمة**: الانتقال للشاشة المقابلة
- **السحب للتحديث**: تحديث البيانات

#### 6. شاشة المدفوعات (العميل):
**المكونات:**
- **إحصائيات المدفوعات**: إجمالي المدفوع، عدد الدفعات
- **فلترة المدفوعات**: حسب التاريخ والمبلغ
- **قائمة المدفوعات**: بطاقات تحتوي على:
  - تاريخ ووقت الدفعة
  - المبلغ المدفوع
  - طريقة الدفع
  - رقم الإيصال
  - حالة الدفعة (مؤكدة، معلقة)

**التفاعلات:**
- **النقر على المدفوعة**: عرض تفاصيل وإيصال
- **النقر على "طباعة"**: تصدير إيصال PDF

#### 7. شاشة حالة الاشتراك:
**المكونات:**
- **بانر حالة الاشتراك**: في أعلى لوحة التحكم دائماً
  - للفترة التجريبية: خلفية زرقاء مع نص "فترة تجريبية - متبقي X أيام"
  - للتحذيرات: خلفية حمراء مع نص "ينتهي اشتراكك خلال X أيام"
  - للاشتراك النشط: خلفية خضراء مع نص "اشتراك نشط حتى تاريخ X"
- **زر معلومات الاشتراك**: للانتقال لتفاصيل الاشتراك
- **عداد الأيام المتبقية**: رقم كبير وواضح
- **شريط تقدم**: يوضح نسبة الفترة المستهلكة

**التفاعلات:**
- **النقر على البانر**: عرض تفاصيل الاشتراك
- **النقر على "تجديد الاشتراك"**: الانتقال لشاشة التواصل

#### 8. شاشة انتهاء الاشتراك:
**المكونات:**
- **شعار التطبيق**: في الأعلى مع اسم "دائن مدين"
- **رسالة الانتهاء**: "انتهت فترتك التجريبية" أو "انتهى اشتراكك"
- **وصف الخدمة**: تذكير بمزايا التطبيق
- **معرف الجهاز**:
  - عرض المعرف في مربع نص
  - زر نسخ مع أيقونة
  - رسالة توضيحية "أرسل هذا المعرف للمطور"
- **معلومات التواصل**:
  - رقم الهاتف مع زر الاتصال المباشر
  - رقم الواتساب مع زر فتح الواتساب
  - البريد الإلكتروني مع زر فتح تطبيق البريد
- **تعليمات الاشتراك**: خطوات واضحة للحصول على الاشتراك

**التفاعلات:**
- **النقر على "نسخ المعرف"**: نسخ معرف الجهاز للحافظة
- **النقر على رقم الهاتف**: فتح تطبيق الهاتف للاتصال
- **النقر على الواتساب**: فتح الواتساب مع رسالة جاهزة
- **النقر على البريد**: فتح تطبيق البريد مع رسالة جاهزة

#### 9. شاشة إنشاء حساب عميل (مالك المنشأة):
**المكونات:**
- **نموذج بيانات العميل**:
  - حقل الاسم الكامل (مطلوب)
  - حقل رقم الهاتف (مطلوب)
  - حقل البريد الإلكتروني (اختياري)
  - حقل العنوان (اختياري)
- **بيانات تسجيل الدخول**:
  - حقل اسم المستخدم (مطلوب، فريد)
  - حقل كلمة المرور (مطلوب)
  - زر إنشاء كلمة مرور عشوائية
- **إعدادات الحساب**:
  - حقل حد الائتمان (رقم)
  - مفتاح تفعيل/إلغاء تفعيل الحساب
- **خيارات الإرسال**:
  - اختيار طريقة إرسال البيانات (SMS أو WhatsApp)
  - معاينة الرسالة قبل الإرسال
- **أزرار العمل**: حفظ وإرسال، إلغاء

**التفاعلات:**
- **النقر على "إنشاء كلمة مرور"**: توليد كلمة مرور قوية تلقائياً
- **النقر على "معاينة الرسالة"**: عرض نص الرسالة التي ستُرسل
- **النقر على "حفظ وإرسال"**: إنشاء الحساب وإرسال البيانات
- **تغيير طريقة الإرسال**: تحديث نموذج الرسالة

### 🧩 العناصر المشتركة (Shared Widgets)

#### بطاقة مخصصة (Custom Card):
```dart
class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final VoidCallback? onTap;

  const CustomCard({
    Key? key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor ?? Theme.of(context).colorScheme.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );
  }
}
```

#### زر مخصص (Custom Button):
```dart
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isLoading;
  final bool isOutlined;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isLoading = false,
    this.isOutlined = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isOutlined) {
      return OutlinedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : Icon(icon),
        label: Text(text),
        style: OutlinedButton.styleFrom(
          foregroundColor: backgroundColor ?? Theme.of(context).colorScheme.primary,
          side: BorderSide(
            color: backgroundColor ?? Theme.of(context).colorScheme.primary,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      );
    }

    return ElevatedButton.icon(
      onPressed: isLoading ? null : onPressed,
      icon: isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(icon),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.primary,
        foregroundColor: textColor ?? Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }
}
```

#### حقل إدخال مخصص (Custom Input Field):
```dart
class CustomTextField extends StatelessWidget {
  final String label;
  final String? hint;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final int? maxLines;

  const CustomTextField({
    Key? key,
    required this.label,
    this.hint,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.maxLines = 1,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      obscureText: obscureText,
      enabled: enabled,
      maxLines: maxLines,
      style: TextStyle(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: Theme.of(context).colorScheme.primary)
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(suffixIcon),
                onPressed: onSuffixIconPressed,
                color: Theme.of(context).colorScheme.primary,
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
      ),
    );
  }
}
```

#### مؤشر التحميل المخصص (Custom Loading Indicator):
```dart
class CustomLoadingIndicator extends StatelessWidget {
  final String? message;
  final Color? color;

  const CustomLoadingIndicator({
    Key? key,
    this.message,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            color: color ?? Theme.of(context).colorScheme.primary,
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onBackground,
                fontSize: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
```

### 🎯 عناصر التفاعل

#### الأزرار:
- **أزرار أساسية**: مستطيلة مع زوايا مدورة
- **أزرار عائمة**: دائرية مع أيقونة
- **أزرار نصية**: للإجراءات الثانوية
- **أزرار الأيقونات**: للإجراءات السريعة

#### البطاقات:
- **بطاقات مرتفعة**: ظل خفيف للعمق
- **زوايا مدورة**: 12px للمظهر الحديث
- **حشو متسق**: 16px داخلياً
- **فواصل واضحة**: بين العناصر

#### النماذج:
- **حقول إدخال محددة**: حدود واضحة
- **تسميات عائمة**: تتحرك عند التركيز
- **رسائل التحقق**: تحت الحقل مباشرة
- **أزرار الإرسال**: في الأسفل مع لون مميز

### 📐 التصميم المتجاوب

#### الهواتف الذكية (< 600px):
- **عمود واحد**: للمحتوى الرئيسي
- **قائمة جانبية**: قابلة للسحب
- **أزرار كبيرة**: سهلة اللمس (48px+)
- **نص مقروء**: حجم خط 16px+

#### الأجهزة اللوحية (600px - 1200px):
- **عمودين**: للمحتوى عند الإمكان
- **قائمة جانبية**: ثابتة أو قابلة للطي
- **استغلال أفضل للمساحة**: عرض أكثر للمعلومات

#### الشاشات الكبيرة (> 1200px):
- **ثلاثة أعمدة**: للمحتوى الغني
- **قائمة جانبية**: ثابتة ومفصلة
- **لوحات متعددة**: عرض متزامن للمعلومات

---

## 5. إدارة المستخدمين والأدوار

### 👥 أنواع المستخدمين

#### 1. مالك المنشأة (Business Owner):
**الوصف**: المالك الرئيسي للمحل أو المنشأة التجارية

**الصلاحيات الكاملة**:
- إدارة جميع العملاء (إضافة، تعديل، حذف، عرض)
- إدارة جميع الديون (تسجيل، تحديث، حذف)
- إدارة المدفوعات (تسجيل، تعديل، إلغاء)
- إدارة الموظفين (إضافة، تحديد الصلاحيات، حذف)
- عرض جميع التقارير والإحصائيات
- إدارة إعدادات المنشأة
- إجراء النسخ الاحتياطية
- تصدير البيانات

**البيانات المطلوبة للتسجيل**:
- اسم المنشأة (مطلوب)
- اسم المالك (مطلوب)
- البريد الإلكتروني (مطلوب، فريد)
- رقم الهاتف (اختياري)
- عنوان المنشأة (اختياري)
- صورة شخصية (اختياري)

#### 2. العميل (Customer):
**الوصف**: الزبون الذي يتعامل مع المنشأة ويحتاج لتتبع ديونه

**الصلاحيات المحدودة**:
- عرض ديونه الشخصية فقط
- عرض مدفوعاته الشخصية فقط
- طلب كشف حساب شخصي
- تحديث بياناته الشخصية
- تلقي الإشعارات والتذكيرات

**البيانات المطلوبة للتسجيل**:
- الاسم الكامل (مطلوب)
- اسم المستخدم (مطلوب، فريد)
- البريد الإلكتروني (مطلوب، فريد)
- رقم الهاتف (اختياري)
- حد الائتمان (يحدده مالك المنشأة)

#### 3. الموظف (Employee):
**الوصف**: موظف في المنشأة مخول بتسجيل بعض المعاملات

**الصلاحيات المحددة** (حسب ما يحدده مالك المنشأة):
- تسجيل ديون جديدة (إذا مسموح)
- تسجيل مدفوعات (إذا مسموح)
- عرض بيانات العملاء (قراءة فقط)
- طباعة الفواتير والإيصالات
- عرض التقارير الأساسية (إذا مسموح)

**البيانات المطلوبة للتسجيل**:
- اسم الموظف (مطلوب)
- البريد الإلكتروني (مطلوب، فريد)
- رقم الهاتف (اختياري)
- المنصب/الوظيفة (اختياري)
- الصلاحيات المحددة (يحددها مالك المنشأة)

### 🔐 نظام الصلاحيات

#### مصفوفة الصلاحيات:

| الوظيفة | مالك المنشأة | الموظف | العميل |
|---------|-------------|--------|--------|
| إدارة العملاء | ✅ كاملة | ❌ | ❌ |
| عرض العملاء | ✅ | ✅ قراءة فقط | ❌ |
| تسجيل الديون | ✅ | ✅ (إذا مسموح) | ❌ |
| عرض الديون | ✅ جميع الديون | ✅ جميع الديون | ✅ ديونه فقط |
| تسجيل المدفوعات | ✅ | ✅ (إذا مسموح) | ❌ |
| عرض المدفوعات | ✅ جميع المدفوعات | ✅ جميع المدفوعات | ✅ مدفوعاته فقط |
| التقارير المالية | ✅ | ✅ (إذا مسموح) | ✅ تقاريره فقط |
| إدارة الموظفين | ✅ | ❌ | ❌ |
| النسخ الاحتياطية | ✅ | ❌ | ❌ |
| إعدادات المنشأة | ✅ | ❌ | ❌ |

### 🔑 عملية التسجيل والمصادقة

#### تسجيل مالك منشأة جديد:
1. **اختيار نوع الحساب**: "مالك منشأة"
2. **إدخال البيانات الأساسية**: اسم المنشأة، اسم المالك، البريد الإلكتروني
3. **إنشاء كلمة مرور قوية**: مع متطلبات الأمان
4. **التحقق من البريد الإلكتروني**: إرسال رابط تفعيل
5. **إكمال الملف الشخصي**: إضافة باقي البيانات
6. **إعداد المصادقة الثنائية**: (اختياري) بصمة أو Face ID

#### إنشاء حساب عميل جديد (عبر مالك المنشأة فقط):
1. **مالك المنشأة يدخل بيانات العميل**: من لوحة إدارة العملاء
2. **إدخال البيانات الأساسية**: الاسم، رقم الهاتف، البريد الإلكتروني (اختياري)
3. **تحديد بيانات الدخول**: اسم المستخدم وكلمة المرور للعميل
4. **تحديد حد الائتمان**: المبلغ الأقصى المسموح للعميل
5. **حفظ الحساب**: إنشاء الحساب في النظام
6. **إرسال بيانات الدخول**: عبر الرسائل النصية أو الواتساب للعميل
7. **العميل يستخدم البيانات المرسلة**: لتسجيل الدخول لأول مرة

**ملاحظة مهمة**: العملاء لا يمكنهم التسجيل الذاتي أو إنشاء حسابات بأنفسهم

#### إضافة موظف:
1. **مالك المنشأة يدخل بيانات الموظف**: من لوحة إدارة الموظفين
2. **تحديد الصلاحيات**: اختيار الوظائف المسموحة
3. **إرسال دعوة**: للموظف عبر البريد الإلكتروني
4. **الموظف ينشئ كلمة مرور**: ويفعل حسابه
5. **بدء العمل**: بالصلاحيات المحددة

### 🛡️ أمان الحسابات

#### متطلبات كلمة المرور:
- **الحد الأدنى**: 8 أحرف
- **التنوع**: أحرف كبيرة وصغيرة وأرقام ورموز
- **منع كلمات المرور الشائعة**: قاموس الكلمات المحظورة
- **انتهاء الصلاحية**: تغيير دوري (اختياري)

#### المصادقة الثنائية:
- **البصمة**: للأجهزة المدعومة
- **Face ID**: للأجهزة المدعومة
- **رمز التحقق**: عبر البريد الإلكتروني أو SMS

#### إدارة الجلسات:
- **انتهاء الجلسة**: بعد فترة عدم نشاط
- **تسجيل خروج تلقائي**: عند إغلاق التطبيق
- **جلسات متعددة**: إمكانية تسجيل الدخول من أجهزة متعددة
- **إنهاء الجلسات البعيدة**: من إعدادات الأمان

### 🔑 نظام الاشتراكات والتفعيل

#### الفترة التجريبية المجانية:

##### عند التسجيل الأول:
- **مدة الفترة التجريبية**: شهر واحد (30 يوم) قابل للتمديد لشهرين حسب السياسة
- **تفعيل تلقائي**: تبدأ الفترة التجريبية فور إنشاء حساب مالك المنشأة
- **وصول كامل**: جميع الميزات متاحة خلال الفترة التجريبية
- **تسجيل تاريخ البداية**: لحساب الأيام المتبقية بدقة

##### عرض حالة الاشتراك:
```dart
class SubscriptionStatus {
  final bool isTrialPeriod;           // هل الحساب في فترة تجريبية
  final DateTime trialStartDate;      // تاريخ بداية الفترة التجريبية
  final DateTime trialEndDate;        // تاريخ انتهاء الفترة التجريبية
  final int daysRemaining;            // عدد الأيام المتبقية
  final bool isActive;                // هل الاشتراك نشط
  final String subscriptionType;      // نوع الاشتراك (trial, premium, expired)

  // حساب الأيام المتبقية
  int get remainingDays {
    if (!isTrialPeriod) return 0;
    final now = DateTime.now();
    final difference = trialEndDate.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  // هل يحتاج تذكير
  bool get needsReminder {
    return remainingDays <= 7 && remainingDays > 0;
  }

  // هل انتهت الفترة التجريبية
  bool get isExpired {
    return DateTime.now().isAfter(trialEndDate) && !isActive;
  }
}
```

#### واجهة عرض حالة الاشتراك:

##### في لوحة التحكم:
- **بانر علوي**: يظهر دائماً في أعلى لوحة التحكم
- **لون مميز**: أزرق للفترة التجريبية، أحمر للتحذيرات
- **معلومات واضحة**: "فترة تجريبية - متبقي X أيام"
- **زر الاشتراك**: رابط مباشر لشاشة معلومات الاشتراك

##### التذكيرات التلقائية:
- **7 أيام قبل الانتهاء**: إشعار يومي مع تفاصيل الاشتراك
- **3 أيام قبل الانتهاء**: إشعار مع معلومات التواصل
- **يوم واحد قبل الانتهاء**: إشعار عاجل مع تعليمات التفعيل
- **عند الانتهاء**: منع الوصول وعرض شاشة الاشتراك

#### شاشة انتهاء الفترة التجريبية:

##### عند انتهاء الفترة:
- **منع الوصول الكامل**: لجميع الوظائف عدا شاشة الاشتراك
- **تسجيل خروج تلقائي**: من جميع الجلسات النشطة
- **رسالة واضحة**: "انتهت فترتك التجريبية - يرجى الاشتراك للمتابعة"
- **عرض معلومات التواصل**: للحصول على الاشتراك المدفوع

#### معلومات التواصل والتفعيل:

##### بيانات التواصل:
```dart
class ContactInfo {
  static const String phoneNumber = '+966501234567';
  static const String email = '<EMAIL>';
  static const String whatsapp = '+966501234567';
  static const String telegramUsername = '@dayen_madeen_support';

  // رسالة جاهزة للإرسال
  static String get subscriptionMessage {
    return 'مرحباً، أريد الاشتراك في تطبيق دائن مدين\n'
           'معرف الجهاز: ${DeviceInfo.getDeviceId()}\n'
           'اسم المنشأة: ${AuthController.instance.businessName}';
  }
}
```

##### معرف الجهاز الفريد:
```dart
class DeviceInfo {
  static String? _deviceId;

  // الحصول على معرف الجهاز الفريد
  static Future<String> getDeviceId() async {
    if (_deviceId != null) return _deviceId!;

    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = androidInfo.id; // Android ID
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      _deviceId = iosInfo.identifierForVendor; // iOS Vendor ID
    }

    return _deviceId ?? 'unknown';
  }

  // نسخ معرف الجهاز للحافظة
  static Future<void> copyDeviceIdToClipboard() async {
    final deviceId = await getDeviceId();
    await Clipboard.setData(ClipboardData(text: deviceId));
  }
}
```

#### واجهة شاشة الاشتراك:

##### المكونات الأساسية:
- **شعار التطبيق**: في الأعلى
- **رسالة الترحيب**: "للمتابعة، يرجى الاشتراك في الخدمة"
- **معرف الجهاز**: مع زر النسخ
- **معلومات التواصل**: أرقام الهاتف والواتساب
- **تعليمات واضحة**: خطوات الاشتراك
- **أزرار التواصل المباشر**: فتح الواتساب أو الاتصال

#### نظام التفعيل الإداري:

##### لوحة تحكم المطور:
```dart
class AdminSubscriptionManager {
  // تفعيل اشتراك جديد
  static Future<bool> activateSubscription({
    required String deviceId,
    required String businessName,
    required SubscriptionPlan plan,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      await SupabaseClient.instance
          .from('subscriptions')
          .insert({
            'device_id': deviceId,
            'business_name': businessName,
            'plan_type': plan.name,
            'start_date': startDate.toIso8601String(),
            'end_date': endDate.toIso8601String(),
            'is_active': true,
            'activated_by': 'admin',
            'activated_at': DateTime.now().toIso8601String(),
          });

      // إرسال إشعار للمستخدم
      await NotificationService.sendActivationNotification(deviceId);

      return true;
    } catch (e) {
      return false;
    }
  }

  // التحقق من حالة الاشتراك
  static Future<SubscriptionStatus> checkSubscriptionStatus(String deviceId) async {
    final response = await SupabaseClient.instance
        .from('subscriptions')
        .select()
        .eq('device_id', deviceId)
        .eq('is_active', true)
        .single();

    if (response.data != null) {
      return SubscriptionStatus.fromJson(response.data);
    }

    return SubscriptionStatus.expired();
  }
}
```

#### التحقق المستمر من الاشتراك:

##### مراقبة حالة الاشتراك:
```dart
class SubscriptionMonitor {
  static Timer? _subscriptionTimer;

  // بدء مراقبة الاشتراك
  static void startMonitoring() {
    _subscriptionTimer = Timer.periodic(
      const Duration(hours: 1), // فحص كل ساعة
      (timer) async {
        final deviceId = await DeviceInfo.getDeviceId();
        final status = await AdminSubscriptionManager.checkSubscriptionStatus(deviceId);

        if (status.isExpired) {
          // إنهاء الجلسة وعرض شاشة الاشتراك
          await AuthController.instance.forceLogout();
          Get.offAllNamed('/subscription-required');
        } else if (status.needsReminder) {
          // عرض تذكير
          NotificationService.showSubscriptionReminder(status.remainingDays);
        }
      },
    );
  }

  // إيقاف المراقبة
  static void stopMonitoring() {
    _subscriptionTimer?.cancel();
    _subscriptionTimer = null;
  }
}
```

---

## 6. إدارة البيانات

### 🗃️ هيكل البيانات المفصل

#### نموذج بيانات مالك المنشأة:
```dart
class BusinessOwner {
  final String id;                    // معرف فريد UUID
  final String authUserId;            // معرف المصادقة من Supabase
  final String businessName;          // اسم المنشأة (مطلوب)
  final String ownerName;             // اسم المالك (مطلوب)
  final String email;                 // البريد الإلكتروني (فريد)
  final String? phone;                // رقم الهاتف (اختياري)
  final String? address;              // عنوان المنشأة (اختياري)
  final String? profileImagePath;     // مسار صورة الملف الشخصي
  final DateTime createdAt;           // تاريخ إنشاء الحساب
  final DateTime updatedAt;           // تاريخ آخر تحديث
}
```

#### نموذج بيانات العميل:
```dart
class Customer {
  final String id;                    // معرف فريد UUID
  final String businessOwnerId;       // معرف مالك المنشأة
  final String authUserId;            // معرف المصادقة من Supabase
  final String name;                  // اسم العميل (مطلوب)
  final String? phone;                // رقم الهاتف (اختياري)
  final String username;              // اسم المستخدم (فريد)
  final String userType;              // نوع المستخدم (customer)
  final double creditLimit;           // حد الائتمان المسموح
  final double currentBalance;        // الرصيد الحالي
  final bool isActive;                // حالة تفعيل الحساب
  final DateTime createdAt;           // تاريخ إنشاء الحساب
  final DateTime updatedAt;           // تاريخ آخر تحديث
}
```

#### نموذج بيانات الدين:
```dart
class Debt {
  final String id;                    // معرف فريد UUID
  final String customerId;            // معرف العميل
  final String businessOwnerId;       // معرف مالك المنشأة
  final double amount;                // مبلغ الدين
  final String? description;          // وصف الدين (اختياري)
  final DateTime dateCreated;         // تاريخ إنشاء الدين
  final bool isPaid;                  // حالة الدفع (مدفوع/غير مدفوع)
  final double paidAmount;            // المبلغ المدفوع (للدفع الجزئي)
  final DateTime createdAt;           // تاريخ الإنشاء في النظام
  final DateTime updatedAt;           // تاريخ آخر تحديث

  // خصائص محسوبة
  double get remainingAmount => amount - paidAmount;
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < amount;
}
```

#### نموذج بيانات المدفوعة:
```dart
class Payment {
  final String id;                    // معرف فريد UUID
  final String debtId;                // معرف الدين المرتبط
  final String customerId;            // معرف العميل
  final String businessOwnerId;       // معرف مالك المنشأة
  final double amount;                // مبلغ المدفوعة
  final DateTime paymentDate;         // تاريخ الدفع
  final String paymentMethod;         // طريقة الدفع (نقدي، بنكي، إلخ)
  final String? notes;                // ملاحظات إضافية
  final DateTime createdAt;           // تاريخ تسجيل المدفوعة
}
```

#### نموذج بيانات الاشتراك:
```dart
class Subscription {
  final String id;                    // معرف فريد UUID
  final String deviceId;              // معرف الجهاز الفريد
  final String businessOwnerId;       // معرف مالك المنشأة
  final String businessName;          // اسم المنشأة
  final String planType;              // نوع الخطة (trial, premium, enterprise)
  final DateTime startDate;           // تاريخ بداية الاشتراك
  final DateTime endDate;             // تاريخ انتهاء الاشتراك
  final bool isActive;                // حالة تفعيل الاشتراك
  final bool isTrial;                 // هل هو اشتراك تجريبي
  final DateTime? trialStartDate;     // تاريخ بداية الفترة التجريبية
  final DateTime? trialEndDate;       // تاريخ انتهاء الفترة التجريبية
  final String? activatedBy;          // من قام بالتفعيل
  final DateTime? activatedAt;        // تاريخ التفعيل
  final DateTime lastChecked;         // آخر فحص للحالة
  final DateTime createdAt;           // تاريخ الإنشاء
  final DateTime updatedAt;           // تاريخ آخر تحديث

  // خصائص محسوبة
  int get daysRemaining {
    final now = DateTime.now();
    final targetDate = isTrial ? (trialEndDate ?? endDate) : endDate;
    final difference = targetDate.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  bool get isExpired {
    final now = DateTime.now();
    final targetDate = isTrial ? (trialEndDate ?? endDate) : endDate;
    return now.isAfter(targetDate) && !isActive;
  }

  bool get needsReminder {
    return daysRemaining <= 7 && daysRemaining > 0;
  }

  String get statusText {
    if (isExpired) return 'منتهي الصلاحية';
    if (isTrial) return 'فترة تجريبية';
    if (isActive) return 'نشط';
    return 'غير نشط';
  }

  Color get statusColor {
    if (isExpired) return Colors.red;
    if (needsReminder) return Colors.orange;
    if (isTrial) return Colors.blue;
    if (isActive) return Colors.green;
    return Colors.grey;
  }

  // إنشاء اشتراك تجريبي جديد
  factory Subscription.createTrial({
    required String deviceId,
    required String businessOwnerId,
    required String businessName,
    int trialDays = 30,
  }) {
    final now = DateTime.now();
    final trialEnd = now.add(Duration(days: trialDays));

    return Subscription(
      id: const Uuid().v4(),
      deviceId: deviceId,
      businessOwnerId: businessOwnerId,
      businessName: businessName,
      planType: 'trial',
      startDate: now,
      endDate: trialEnd,
      isActive: true,
      isTrial: true,
      trialStartDate: now,
      trialEndDate: trialEnd,
      activatedBy: 'system',
      activatedAt: now,
      lastChecked: now,
      createdAt: now,
      updatedAt: now,
    );
  }
}
```

### 🔗 العلاقات بين الجداول

#### العلاقات الأساسية:
```sql
-- علاقة واحد لمتعدد: مالك منشأة واحد لعدة عملاء
business_owners (1) ←→ (N) customers

-- علاقة واحد لمتعدد: عميل واحد لعدة ديون
customers (1) ←→ (N) debts

-- علاقة واحد لمتعدد: دين واحد لعدة مدفوعات (للدفع الجزئي)
debts (1) ←→ (N) payments

-- علاقة واحد لمتعدد: مالك منشأة واحد لعدة ديون (للتتبع)
business_owners (1) ←→ (N) debts

-- علاقة واحد لمتعدد: مالك منشأة واحد لعدة مدفوعات (للتتبع)
business_owners (1) ←→ (N) payments
```

#### فهارس قاعدة البيانات:
```sql
-- فهارس للأداء السريع
CREATE INDEX idx_customers_business_owner ON customers(business_owner_id);
CREATE INDEX idx_debts_customer ON debts(customer_id);
CREATE INDEX idx_debts_business_owner ON debts(business_owner_id);
CREATE INDEX idx_debts_date_created ON debts(date_created);
CREATE INDEX idx_payments_debt ON payments(debt_id);
CREATE INDEX idx_payments_customer ON payments(customer_id);
CREATE INDEX idx_payments_date ON payments(payment_date);
```

### 💾 استراتيجية التخزين

#### التخزين السحابي (Supabase):
- **البيانات الأساسية**: جميع المعاملات والحسابات
- **الصور والملفات**: صور الملفات الشخصية والفواتير
- **النسخ الاحتياطية**: نسخ تلقائية يومية
- **السجلات**: تتبع جميع التغييرات والعمليات

#### التخزين المحلي (Hive):
- **البيانات المؤقتة**: للعمل بدون إنترنت
- **إعدادات المستخدم**: تفضيلات الواجهة واللغة
- **بيانات تسجيل الدخول**: (مشفرة) لتسجيل الدخول السريع
- **الكاش**: البيانات المستخدمة بكثرة

#### تزامن البيانات:
```dart
// استراتيجية التزامن
class DataSyncService {
  // تزامن تلقائي كل 5 دقائق
  static const Duration syncInterval = Duration(minutes: 5);

  // تزامن فوري للعمليات الحساسة
  Future<void> syncCriticalData() async {
    await syncDebts();
    await syncPayments();
    await syncCustomers();
  }

  // تزامن في الخلفية للبيانات الأقل أهمية
  Future<void> syncBackgroundData() async {
    await syncNotifications();
    await syncReports();
  }
}
```

### 🔄 آلية النسخ الاحتياطية والاستعادة

#### النسخ الاحتياطية التلقائية:
- **يومياً**: نسخة كاملة من جميع البيانات
- **أسبوعياً**: نسخة مضغوطة للأرشيف
- **شهرياً**: نسخة طويلة المدى مع التقارير

#### النسخ الاحتياطية اليدوية:
- **نسخة فورية**: عند الطلب من المستخدم
- **نسخة مخصصة**: اختيار البيانات المطلوبة
- **تصدير البيانات**: بصيغ مختلفة (JSON, CSV, PDF)

#### استعادة البيانات:
```dart
class BackupService {
  // إنشاء نسخة احتياطية
  Future<String> createBackup({
    required String businessOwnerId,
    bool includeImages = true,
    bool includeReports = false,
  }) async {
    final backup = {
      'metadata': {
        'version': '1.0',
        'created_at': DateTime.now().toIso8601String(),
        'business_owner_id': businessOwnerId,
      },
      'customers': await getCustomersData(businessOwnerId),
      'debts': await getDebtsData(businessOwnerId),
      'payments': await getPaymentsData(businessOwnerId),
    };

    return await saveBackupFile(backup);
  }

  // استعادة من نسخة احتياطية
  Future<bool> restoreBackup(String backupPath) async {
    try {
      final backup = await loadBackupFile(backupPath);
      await validateBackup(backup);
      await restoreData(backup);
      return true;
    } catch (e) {
      return false;
    }
  }
}
```

### 🔒 أمان البيانات

#### تشفير البيانات الحساسة:
```dart
class DataEncryption {
  // تشفير البيانات المالية
  static String encryptFinancialData(String data) {
    return AESEncryption.encrypt(data, getEncryptionKey());
  }

  // فك تشفير البيانات المالية
  static String decryptFinancialData(String encryptedData) {
    return AESEncryption.decrypt(encryptedData, getEncryptionKey());
  }

  // تشفير كلمات المرور
  static String hashPassword(String password) {
    return bcrypt.hashpw(password, bcrypt.gensalt());
  }
}
```

#### التحقق من سلامة البيانات:
```dart
class DataIntegrity {
  // التحقق من صحة البيانات المالية
  static bool validateFinancialData(Debt debt, List<Payment> payments) {
    double totalPaid = payments.fold(0, (sum, payment) => sum + payment.amount);
    return totalPaid <= debt.amount;
  }

  // التحقق من تطابق الأرصدة
  static bool validateCustomerBalance(Customer customer, List<Debt> debts) {
    double calculatedBalance = debts
        .where((debt) => !debt.isPaid)
        .fold(0, (sum, debt) => sum + debt.remainingAmount);
    return (calculatedBalance - customer.currentBalance).abs() < 0.01;
  }
}
```

---

## 7. التقارير والإحصائيات

### 📊 أنواع التقارير المطلوبة

#### 1. تقارير الديون:

##### تقرير الديون الشامل:
- **المحتوى**: جميع الديون مع تفاصيل كاملة
- **الفلترة**: حسب التاريخ، العميل، الحالة، المبلغ
- **التجميع**: حسب العميل، الشهر، حالة الدفع
- **الإحصائيات**: إجمالي الديون، المعلق، المدفوع، النسب المئوية

##### تقرير الديون المعلقة:
- **المحتوى**: الديون غير المدفوعة فقط
- **الترتيب**: حسب تاريخ الاستحقاق أو المبلغ
- **التنبيهات**: الديون المتأخرة بألوان مميزة
- **إجراءات مقترحة**: قائمة بالعملاء المطلوب تذكيرهم

##### تقرير الديون حسب العميل:
- **المحتوى**: ديون عميل محدد مع التاريخ الكامل
- **الإحصائيات**: إجمالي الديون، المدفوع، المتبقي، متوسط المبلغ
- **الرسوم البيانية**: تطور الديون عبر الزمن
- **التوقعات**: نمط الدفع المتوقع

#### 2. تقارير المدفوعات:

##### تقرير المدفوعات اليومي:
- **المحتوى**: جميع المدفوعات في يوم محدد
- **التفاصيل**: العميل، المبلغ، طريقة الدفع، الوقت
- **الإجمالي**: مجموع المدفوعات اليومية
- **المقارنة**: مع الأيام السابقة

##### تقرير المدفوعات الشهري:
- **المحتوى**: ملخص المدفوعات لشهر كامل
- **التوزيع**: حسب الأيام والأسابيع
- **الاتجاهات**: نمو أو انخفاض المدفوعات
- **أفضل العملاء**: الأكثر دفعاً في الشهر

##### تقرير طرق الدفع:
- **التوزيع**: نسبة كل طريقة دفع (نقدي، بنكي، إلخ)
- **الاتجاهات**: تغير تفضيلات العملاء
- **التحليل**: أكثر الطرق استخداماً

#### 3. التقارير المالية:

##### تقرير الأرباح والخسائر:
- **الإيرادات**: إجمالي المدفوعات
- **المصروفات**: التكاليف التشغيلية (إن وجدت)
- **صافي الربح**: الفرق بين الإيرادات والمصروفات
- **الهوامش**: نسب الربحية

##### تقرير التدفق النقدي:
- **التدفق الداخل**: المدفوعات المستلمة
- **التدفق الخارج**: المبالغ المستردة أو الملغاة
- **صافي التدفق**: الفرق الإجمالي
- **التوقعات**: التدفق المتوقع للفترة القادمة

##### تقرير الديون المعدومة:
- **الديون المشكوك فيها**: المتأخرة لفترة طويلة
- **الديون المعدومة**: المحذوفة أو الملغاة
- **التأثير المالي**: الخسائر المترتبة
- **الإجراءات المقترحة**: خطط الاستعادة

#### 4. تقارير العملاء:

##### تقرير أداء العملاء:
- **أفضل العملاء**: الأكثر دفعاً ونشاطاً
- **العملاء المتأخرين**: الذين لديهم ديون معلقة
- **العملاء الجدد**: المسجلين حديثاً
- **العملاء غير النشطين**: الذين لم يتعاملوا لفترة

##### تقرير ولاء العملاء:
- **مدة التعامل**: كم من الوقت يتعامل كل عميل
- **تكرار المعاملات**: عدد المعاملات لكل عميل
- **متوسط المبلغ**: متوسط قيمة المعاملة لكل عميل
- **نمط الدفع**: سرعة الدفع وانتظامه

### 📈 المؤشرات والإحصائيات المهمة

#### مؤشرات الأداء الرئيسية (KPIs):

##### المؤشرات المالية:
- **إجمالي الديون المعلقة**: مجموع الديون غير المدفوعة
- **معدل التحصيل**: نسبة الديون المحصلة إلى الإجمالي
- **متوسط فترة التحصيل**: الوقت المتوسط لتحصيل الدين
- **معدل الديون المعدومة**: نسبة الديون غير القابلة للتحصيل

##### مؤشرات العملاء:
- **عدد العملاء النشطين**: العملاء الذين تعاملوا مؤخراً
- **معدل نمو العملاء**: زيادة عدد العملاء شهرياً
- **متوسط قيمة العميل**: إجمالي معاملات العميل مقسوماً على عددها
- **معدل احتفاظ العملاء**: نسبة العملاء المستمرين

##### مؤشرات التشغيل:
- **عدد المعاملات اليومية**: متوسط المعاملات في اليوم
- **وقت معالجة المعاملة**: الوقت المتوسط لإتمام المعاملة
- **معدل الأخطاء**: نسبة المعاملات التي تحتاج تصحيح
- **رضا العملاء**: تقييم العملاء للخدمة

#### الرسوم البيانية التفاعلية:

##### رسم بياني للديون عبر الزمن:
```dart
class DebtTrendChart {
  final List<DebtDataPoint> data;
  final DateRange dateRange;

  Widget buildChart() {
    return LineChart(
      LineChartData(
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            axisNameWidget: Text('المبلغ (ريال)'),
            sideTitles: SideTitles(showTitles: true),
          ),
          bottomTitles: AxisTitles(
            axisNameWidget: Text('التاريخ'),
            sideTitles: SideTitles(showTitles: true),
          ),
        ),
        lineBarsData: [
          LineChartBarData(
            spots: data.map((point) => FlSpot(
              point.date.millisecondsSinceEpoch.toDouble(),
              point.amount,
            )).toList(),
            color: Colors.blue,
            barWidth: 3,
          ),
        ],
      ),
    );
  }
}
```

##### رسم دائري لتوزيع الديون:
```dart
class DebtDistributionPieChart {
  final Map<String, double> distribution;

  Widget buildChart() {
    return PieChart(
      PieChartData(
        sections: distribution.entries.map((entry) {
          return PieChartSectionData(
            value: entry.value,
            title: '${entry.key}\n${entry.value.toStringAsFixed(0)} ر.س',
            color: getColorForCategory(entry.key),
            radius: 100,
          );
        }).toList(),
      ),
    );
  }
}
```

### 📤 تصدير البيانات

#### صيغ التصدير المدعومة:

##### PDF:
- **التقارير الرسمية**: بتصميم احترافي مع شعار المنشأة
- **الفواتير**: تصميم قابل للطباعة
- **كشوف الحساب**: للعملاء مع تفاصيل كاملة

##### Excel (XLSX):
- **البيانات الخام**: للتحليل المتقدم
- **الجداول المحورية**: تحليل تفاعلي
- **الرسوم البيانية**: مدمجة في الملف

##### CSV:
- **البيانات البسيطة**: للاستيراد في أنظمة أخرى
- **التكامل**: مع أنظمة المحاسبة الخارجية

##### JSON:
- **النسخ الاحتياطية**: بصيغة قابلة للقراءة آلياً
- **التكامل مع APIs**: لأنظمة أخرى

#### مثال على تصدير التقرير:
```dart
class ReportExportService {
  Future<String> exportToPDF(Report report) async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس التقرير
              pw.Header(
                level: 0,
                child: pw.Text(
                  report.title,
                  style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
                ),
              ),

              // معلومات التقرير
              pw.Paragraph(text: 'تاريخ التقرير: ${report.generatedAt}'),
              pw.Paragraph(text: 'الفترة: ${report.dateRange}'),

              // البيانات
              pw.Table.fromTextArray(
                headers: report.headers,
                data: report.data,
              ),

              // الإحصائيات
              pw.Paragraph(text: 'الإجمالي: ${report.total} ريال'),
            ],
          );
        },
      ),
    );

    final output = await getTemporaryDirectory();
    final file = File('${output.path}/report_${DateTime.now().millisecondsSinceEpoch}.pdf');
    await file.writeAsBytes(await pdf.save());

    return file.path;
  }
}

---

## 8. متطلبات الأداء والجودة

### ⚡ متطلبات الأداء

#### سرعة الاستجابة المطلوبة:

##### واجهة المستخدم:
- **تحميل الشاشة الرئيسية**: أقل من 2 ثانية
- **الانتقال بين الشاشات**: أقل من 500 مللي ثانية
- **تحديث البيانات**: أقل من 3 ثواني
- **البحث في البيانات**: أقل من 1 ثانية لـ 1000 سجل

##### العمليات المالية:
- **تسجيل دين جديد**: أقل من 2 ثانية
- **تسجيل مدفوعة**: أقل من 2 ثانية
- **تحديث حالة الدين**: أقل من 1 ثانية
- **إنشاء التقارير**: أقل من 5 ثواني للتقرير الشهري

##### المزامنة والنسخ الاحتياطية:
- **مزامنة البيانات**: أقل من 10 ثواني للبيانات الأساسية
- **النسخة الاحتياطية**: أقل من 30 ثانية لـ 1000 معاملة
- **استعادة البيانات**: أقل من 60 ثانية للاستعادة الكاملة

#### استهلاك الموارد:

##### الذاكرة (RAM):
- **الحد الأدنى**: 2 جيجابايت للتشغيل الأساسي
- **الموصى به**: 4 جيجابايت للأداء الأمثل
- **استهلاك التطبيق**: أقل من 200 ميجابايت في الحالة العادية
- **ذروة الاستهلاك**: أقل من 500 ميجابايت عند إنشاء التقارير

##### التخزين:
- **حجم التطبيق**: أقل من 100 ميجابايت
- **البيانات المحلية**: 10-50 ميجابايت حسب حجم النشاط
- **الصور والملفات**: حسب الاستخدام (اختياري)
- **النسخ الاحتياطية**: 5-20 ميجابايت لكل نسخة

##### البطارية:
- **الاستهلاك العادي**: أقل من 5% في الساعة للاستخدام المتوسط
- **وضع الخلفية**: أقل من 1% في الساعة
- **المزامنة**: استهلاك إضافي أقل من 2% يومياً

### 👥 عدد المستخدمين المتوقع

#### السيناريوهات المختلفة:

##### المنشأة الصغيرة:
- **أصحاب المنشآت**: 1
- **العملاء**: 50-200 عميل
- **الموظفين**: 0-2 موظف
- **المعاملات اليومية**: 10-50 معاملة
- **حجم البيانات**: 1-5 ميجابايت سنوياً

##### المنشأة المتوسطة:
- **أصحاب المنشآت**: 1-2
- **العملاء**: 200-1000 عميل
- **الموظفين**: 2-10 موظف
- **المعاملات اليومية**: 50-200 معاملة
- **حجم البيانات**: 5-25 ميجابايت سنوياً

##### المنشأة الكبيرة:
- **أصحاب المنشآت**: 2-5
- **العملاء**: 1000-5000 عميل
- **الموظفين**: 10-50 موظف
- **المعاملات اليومية**: 200-1000 معاملة
- **حجم البيانات**: 25-100 ميجابايت سنوياً

#### التوسع والنمو:
```dart
class ScalabilityPlanning {
  // خطة التوسع التلقائي
  static const Map<String, int> scalingLimits = {
    'customers_per_business': 10000,      // عدد العملاء لكل منشأة
    'debts_per_customer': 1000,           // عدد الديون لكل عميل
    'payments_per_debt': 100,             // عدد المدفوعات لكل دين
    'concurrent_users': 50,               // المستخدمين المتزامنين
    'daily_transactions': 2000,           // المعاملات اليومية
  };

  // مراقبة الأداء
  static Future<PerformanceMetrics> monitorPerformance() async {
    return PerformanceMetrics(
      responseTime: await measureResponseTime(),
      memoryUsage: await measureMemoryUsage(),
      cpuUsage: await measureCpuUsage(),
      networkLatency: await measureNetworkLatency(),
    );
  }
}
```

### 📱 متطلبات التوافق مع الأجهزة

#### أنظمة التشغيل المدعومة:

##### Android:
- **الحد الأدنى**: Android 7.0 (API Level 24)
- **الموصى به**: Android 10.0+ (API Level 29+)
- **الدعم الكامل**: Android 12.0+ (API Level 31+)

##### iOS:
- **الحد الأدنى**: iOS 12.0
- **الموصى به**: iOS 14.0+
- **الدعم الكامل**: iOS 15.0+

#### مواصفات الأجهزة:

##### الحد الأدنى:
- **المعالج**: ثنائي النواة 1.5 جيجاهرتز
- **الذاكرة**: 2 جيجابايت RAM
- **التخزين**: 1 جيجابايت مساحة فارغة
- **الشاشة**: 5 بوصة، دقة 720p
- **الاتصال**: Wi-Fi أو بيانات الجوال

##### الموصى به:
- **المعالج**: رباعي النواة 2.0 جيجاهرتز+
- **الذاكرة**: 4 جيجابايت RAM+
- **التخزين**: 2 جيجابايت مساحة فارغة+
- **الشاشة**: 6 بوصة، دقة 1080p+
- **الاتصال**: Wi-Fi سريع أو 4G/5G

#### الميزات الاختيارية:
- **البصمة**: لتسجيل الدخول السريع
- **Face ID**: للأجهزة المدعومة
- **NFC**: للمدفوعات المستقبلية
- **الكاميرا**: لمسح الباركود أو التوقيعات
- **GPS**: لتحديد موقع المعاملات

### 🧪 معايير الجودة والاختبار

#### أنواع الاختبارات المطلوبة:

##### اختبارات الوحدة (Unit Tests):
```dart
// مثال على اختبار وحدة للحسابات المالية
void main() {
  group('Debt Calculations', () {
    test('should calculate remaining amount correctly', () {
      final debt = Debt.create(
        customerId: 'customer1',
        businessOwnerId: 'business1',
        amount: 1000.0,
      );

      expect(debt.remainingAmount, equals(1000.0));

      final updatedDebt = debt.copyWith(paidAmount: 300.0);
      expect(updatedDebt.remainingAmount, equals(700.0));
    });

    test('should validate payment amount', () {
      final debt = Debt.create(
        customerId: 'customer1',
        businessOwnerId: 'business1',
        amount: 1000.0,
      );

      expect(() => debt.addPayment(1500.0), throwsException);
      expect(() => debt.addPayment(500.0), returnsNormally);
    });
  });
}
```

##### اختبارات التكامل (Integration Tests):
```dart
// مثال على اختبار تكامل لعملية تسجيل الدين
void main() {
  group('Debt Management Integration', () {
    testWidgets('should create debt and update customer balance', (tester) async {
      // إعداد البيانات الأولية
      final customer = await createTestCustomer();
      final initialBalance = customer.currentBalance;

      // تشغيل التطبيق
      await tester.pumpWidget(MyApp());

      // تسجيل الدخول
      await loginAsBusinessOwner(tester);

      // الانتقال لشاشة إضافة دين
      await tester.tap(find.byKey(Key('add_debt_button')));
      await tester.pumpAndSettle();

      // إدخال بيانات الدين
      await tester.enterText(find.byKey(Key('amount_field')), '500');
      await tester.enterText(find.byKey(Key('description_field')), 'Test debt');

      // حفظ الدين
      await tester.tap(find.byKey(Key('save_debt_button')));
      await tester.pumpAndSettle();

      // التحقق من النتائج
      final updatedCustomer = await getCustomer(customer.id);
      expect(updatedCustomer.currentBalance, equals(initialBalance + 500));
    });
  });
}
```

##### اختبارات الأداء (Performance Tests):
```dart
// مثال على اختبار الأداء لتحميل البيانات
void main() {
  group('Performance Tests', () {
    test('should load 1000 debts in less than 2 seconds', () async {
      final stopwatch = Stopwatch()..start();

      final debts = await DebtService.getDebts(limit: 1000);

      stopwatch.stop();

      expect(debts.length, equals(1000));
      expect(stopwatch.elapsedMilliseconds, lessThan(2000));
    });

    test('should sync data in less than 10 seconds', () async {
      final stopwatch = Stopwatch()..start();

      await DataSyncService.syncAllData();

      stopwatch.stop();

      expect(stopwatch.elapsedMilliseconds, lessThan(10000));
    });
  });
}
```

#### معايير قبول الجودة:

##### التغطية بالاختبارات:
- **اختبارات الوحدة**: 90%+ تغطية للكود الأساسي
- **اختبارات التكامل**: 80%+ تغطية للوظائف الرئيسية
- **اختبارات واجهة المستخدم**: 70%+ تغطية للشاشات المهمة

##### معدل الأخطاء:
- **أخطاء حرجة**: 0% (لا يُسمح بأخطاء تؤثر على البيانات المالية)
- **أخطاء متوسطة**: أقل من 1% (أخطاء تؤثر على تجربة المستخدم)
- **أخطاء بسيطة**: أقل من 5% (أخطاء تجميلية أو غير مؤثرة)

##### الاستقرار:
- **معدل الانهيار**: أقل من 0.1% من الجلسات
- **معدل الاستجابة**: 99.9% من الطلبات تتم بنجاح
- **وقت التشغيل**: 99.5% من الوقت متاح للاستخدام

### 🔧 أدوات المراقبة والتحليل

#### مراقبة الأداء:
```dart
class PerformanceMonitoring {
  static void trackScreenLoad(String screenName) {
    final stopwatch = Stopwatch()..start();

    // تتبع وقت تحميل الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      stopwatch.stop();
      Analytics.logEvent('screen_load_time', {
        'screen_name': screenName,
        'load_time_ms': stopwatch.elapsedMilliseconds,
      });
    });
  }

  static void trackDatabaseOperation(String operation, Future<void> future) async {
    final stopwatch = Stopwatch()..start();

    try {
      await future;
      stopwatch.stop();

      Analytics.logEvent('database_operation', {
        'operation': operation,
        'duration_ms': stopwatch.elapsedMilliseconds,
        'status': 'success',
      });
    } catch (e) {
      stopwatch.stop();

      Analytics.logEvent('database_operation', {
        'operation': operation,
        'duration_ms': stopwatch.elapsedMilliseconds,
        'status': 'error',
        'error': e.toString(),
      });

      rethrow;
    }
  }
}
```

#### تحليل استخدام التطبيق:
```dart
class UsageAnalytics {
  static void trackFeatureUsage(String feature) {
    Analytics.logEvent('feature_used', {
      'feature_name': feature,
      'timestamp': DateTime.now().toIso8601String(),
      'user_type': AuthController.instance.userType,
    });
  }

  static void trackUserFlow(String fromScreen, String toScreen) {
    Analytics.logEvent('navigation', {
      'from_screen': fromScreen,
      'to_screen': toScreen,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  static void trackError(String error, String context) {
    Analytics.logEvent('error_occurred', {
      'error_message': error,
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
      'user_type': AuthController.instance.userType,
    });
  }
}
```

---

## 🎯 خلاصة المتطلبات

### ✅ قائمة التحقق النهائية

#### المتطلبات الوظيفية:
- [ ] نظام مصادقة متكامل (بريد إلكتروني، بصمة، Face ID)
- [ ] لوحة تحكم مالك المنشأة مع جميع الإحصائيات
- [ ] إدارة شاملة للعملاء (إضافة، تعديل، حذف، عرض)
- [ ] إدارة متقدمة للديون (تسجيل، تحديث، تتبع)
- [ ] نظام مدفوعات مرن (كامل، جزئي، متعدد الطرق)
- [ ] لوحة تحكم العميل مع عرض البيانات الشخصية
- [ ] نظام تقارير شامل مع تصدير متعدد الصيغ
- [ ] إشعارات ذكية وتذكيرات تلقائية

#### المتطلبات التقنية:
- [ ] Flutter 3.24+ مع Dart 3.0+
- [ ] GetX لإدارة الحالة والتنقل
- [ ] Supabase كقاعدة بيانات سحابية
- [ ] Hive للتخزين المحلي
- [ ] نظام أمان متقدم مع RLS
- [ ] دعم العمل بدون إنترنت
- [ ] مزامنة تلقائية للبيانات
- [ ] نسخ احتياطية آمنة

#### متطلبات الجودة:
- [ ] أداء سريع (< 2 ثانية لتحميل الشاشات)
- [ ] استهلاك ذاكرة منخفض (< 200 ميجابايت)
- [ ] دعم الأجهزة القديمة (Android 7.0+, iOS 12.0+)
- [ ] تغطية اختبارات عالية (90%+ للكود الأساسي)
- [ ] معدل أخطاء منخفض (< 0.1% انهيار)
- [ ] واجهة مستخدم متجاوبة وسهلة الاستخدام

### 🚀 خطة التنفيذ المقترحة

#### المرحلة الأولى (4 أسابيع): الأساسيات
1. **إعداد البيئة التطويرية** والمشروع الأساسي
2. **تطوير نظام المصادقة** الكامل
3. **إنشاء قاعدة البيانات** وسياسات الأمان (تشمل جداول الاشتراكات)
4. **تطوير نظام الاشتراكات والفترة التجريبية**:
   - تفعيل الفترة التجريبية التلقائية
   - شاشة عرض حالة الاشتراك
   - نظام التذكيرات
   - شاشة انتهاء الاشتراك
5. **تطوير الشاشات الأساسية** (تسجيل دخول، لوحات التحكم)

#### المرحلة الثانية (6 أسابيع): الوظائف الأساسية
1. **إدارة العملاء** (النظام الجديد):
   - شاشة إنشاء حساب عميل من قبل مالك المنشأة
   - تحديد اسم المستخدم وكلمة المرور للعميل
   - نظام إرسال بيانات الدخول عبر SMS أو WhatsApp
   - إعادة إرسال بيانات الدخول عند الحاجة
2. **إدارة الديون** (تسجيل، تحديث، تتبع)
3. **نظام المدفوعات** (تسجيل، ربط بالديون)
4. **التقارير الأساسية** (ديون، مدفوعات، عملاء)

#### المرحلة الثالثة (4 أسابيع): الميزات المتقدمة
1. **التقارير المتقدمة** مع الرسوم البيانية
2. **نظام الإشعارات** والتذكيرات
3. **تصدير البيانات** بصيغ متعددة
4. **النسخ الاحتياطية** والاستعادة

#### المرحلة الرابعة (3 أسابيع): التحسين والاختبار
1. **تحسين الأداء** والاستجابة
2. **اختبارات شاملة** (وحدة، تكامل، أداء)
3. **تحسين واجهة المستخدم** والتجربة
4. **التوثيق النهائي** ودليل المستخدم

#### المرحلة الخامسة (2 أسابيع): النشر والإطلاق
1. **اختبارات ما قبل الإطلاق** النهائية
2. **إعداد البيئة الإنتاجية** والخوادم
3. **نشر التطبيق** على المتاجر
4. **المراقبة والدعم** الأولي

---

## 📞 معلومات الاتصال والدعم

**تم إعداد هذه الوثيقة بواسطة**: فريق تطوير تطبيق "دائن مدين"
**تاريخ الإعداد**: ديسمبر 2024
**الإصدار**: 1.0

**للاستفسارات التقنية**: يرجى الرجوع لهذه الوثيقة أولاً، ثم التواصل مع فريق التطوير
**للتحديثات**: ستتم مراجعة هذه الوثيقة وتحديثها حسب متطلبات المشروع

---

---

## 🎨 ملخص التحديثات الأخيرة - نظام إدارة التصميم المركزي

### التحديث الجديد - نظام إدارة التصميم المركزي:

#### ✅ **الهدف من النظام المركزي**:
- **سهولة الصيانة**: تغيير واحد يؤثر على التطبيق بأكمله
- **الاتساق**: ضمان توحيد التصميم عبر جميع الشاشات
- **الأداء**: استخدام const values لتحسين الأداء
- **قابلية التوسع**: إضافة عناصر جديدة بسهولة

#### ✅ **الملفات المركزية الجديدة**:
1. **app_colors.dart**: جميع ألوان التطبيق مع دوال مساعدة
2. **app_icons.dart**: جميع أيقونات التطبيق مع إنشاء أيقونات مخصصة
3. **app_text_styles.dart**: جميع أنماط النصوص مع دعم الوضع الداكن
4. **app_decorations.dart**: جميع التزيينات والحدود مع دوال مساعدة

#### ✅ **المميزات الرئيسية**:
- **تحديث مركزي**: تغيير لون واحد يحدث التطبيق بأكمله
- **دوال مساعدة**: إنشاء عناصر مخصصة بسهولة
- **دعم الثيمات**: تلقائي للوضع الليلي والنهاري
- **أمثلة عملية**: كيفية استخدام النظام في الكود الفعلي
- **إرشادات الصيانة**: كيفية إضافة عناصر جديدة

#### ✅ **فوائد النظام**:
- **صيانة أسهل**: لا حاجة للبحث في ملفات متعددة
- **اتساق مضمون**: جميع الشاشات تستخدم نفس العناصر
- **أداء محسن**: استخدام const values وعدم تكرار الكائنات
- **تطوير أسرع**: إضافة ميزات جديدة بسرعة أكبر

### التحديثات السابقة في التصميم:

#### ✅ **نظام الألوان المحدث**:
- **اللون الأساسي**: الأبيض (#FFFFFF) كخلفية رئيسية
- **اللون الثانوي**: الأزرق الكحلي (#1E3A8A, #0F172A) للعناصر المهمة
- **ألوان مساعدة**: درجات من الرمادي والأزرق الفاتح
- **ألوان الحالة**: موحدة للوضعين (أخضر، برتقالي، أحمر، أزرق)

#### ✅ **دعم الوضع الليلي والنهاري**:
- **الوضع النهاري**: خلفية بيضاء، نصوص زرقاء كحلية
- **الوضع الليلي**: خلفية داكنة، نصوص بيضاء/رمادي فاتح
- **تبديل تلقائي**: حسب إعدادات النظام أو اختيار المستخدم
- **حفظ التفضيلات**: تذكر اختيار المستخدم

#### ✅ **نظام الثيمات المتقدم**:
- **ThemeController**: لإدارة التبديل بين الثيمات
- **AppThemes**: كلاس موحد للثيمات
- **AppColors**: نظام ألوان شامل ومنظم
- **Material Design 3**: تطبيق أحدث معايير التصميم

### التحديثات في البرمجة والهيكل:

#### ✅ **هيكل المشروع المحسن**:
- **تنظيم أفضل**: فصل واضح بين الطبقات
- **Modular Architecture**: كل وحدة في مجلد منفصل
- **Separation of Concerns**: فصل المنطق عن الواجهة
- **Clean Code**: كود نظيف وقابل للصيانة

#### ✅ **نظام إدارة الحالة المتقدم**:
- **GetX Controllers**: لكل وحدة controller منفصل
- **Reactive Programming**: استخدام Obx للتحديثات التفاعلية
- **Dependency Injection**: حقن التبعيات بطريقة منظمة
- **State Persistence**: حفظ الحالة عند إغلاق التطبيق

#### ✅ **العناصر المشتركة (Shared Widgets)**:
- **CustomCard**: بطاقة موحدة بالتصميم الجديد
- **CustomButton**: أزرار متناسقة مع الثيمات
- **CustomTextField**: حقول إدخال موحدة
- **CustomLoadingIndicator**: مؤشر تحميل مخصص

#### ✅ **نظام التنقل المحسن**:
- **Named Routes**: مسارات واضحة ومنظمة
- **Middlewares**: للتحقق من المصادقة والصلاحيات
- **Bindings**: ربط التبعيات لكل صفحة
- **Route Guards**: حماية الصفحات الحساسة

### التركيز الحالي:

#### 🎯 **ما يتم التركيز عليه الآن**:
- **تصميم واجهات المستخدم**: UI/UX Design
- **هيكل الكود والبرمجة**: Code Architecture
- **نظام إدارة الحالة**: State Management
- **التنقل بين الشاشات**: Navigation System
- **العناصر المشتركة**: Reusable Components
- **نظام الثيمات**: Theme Management

#### ⏳ **ما سيتم إضافته لاحقاً**:
- **قاعدة البيانات**: Database Integration
- **APIs والخدمات الخارجية**: External Services
- **المصادقة السحابية**: Cloud Authentication
- **النسخ الاحتياطية**: Backup Systems
- **الإشعارات**: Push Notifications

---

## 🔄 ملخص التحديثات السابقة

### التغييرات الرئيسية في نظام إدارة المستخدمين:

#### ✅ **تعديل نظام تسجيل العملاء**:
- **إلغاء التسجيل الذاتي**: العملاء لا يمكنهم إنشاء حسابات بأنفسهم
- **التسجيل عبر مالك المنشأة**: مالك المنشأة هو الوحيد المخول بإنشاء حسابات العملاء
- **إرسال بيانات الدخول**: عبر SMS أو WhatsApp للعميل
- **شاشة إنشاء حساب عميل**: واجهة جديدة لمالك المنشأة

#### ✅ **نظام الاشتراكات والفترة التجريبية**:
- **فترة تجريبية مجانية**: 30 يوم تلقائياً عند التسجيل الأول
- **عرض حالة الاشتراك**: بانر دائم في لوحة التحكم
- **تذكيرات تلقائية**: قبل 7، 3، 1 أيام من الانتهاء
- **منع الوصول**: عند انتهاء الفترة التجريبية
- **شاشة طلب الاشتراك**: مع معرف الجهاز ومعلومات التواصل

#### ✅ **التحديثات التقنية**:
- **جداول قاعدة بيانات جديدة**: subscriptions, subscription_logs, subscription_notifications
- **نماذج بيانات جديدة**: Subscription, DeviceInfo, ContactInfo
- **خدمات جديدة**: SubscriptionMonitoring, CustomerCredentials, DeviceIdentification
- **سياسات أمان محدثة**: لحماية بيانات الاشتراكات

#### ✅ **واجهات مستخدم جديدة**:
- **شاشة حالة الاشتراك**: عرض الأيام المتبقية والحالة
- **شاشة انتهاء الاشتراك**: معلومات التواصل ومعرف الجهاز
- **شاشة إنشاء حساب عميل**: للمالك مع خيارات الإرسال
- **بانر الاشتراك**: في لوحة التحكم الرئيسية

### 📋 **قائمة التحقق المحدثة**:

#### المتطلبات الجديدة - نظام إدارة التصميم المركزي:
- [ ] **ملف الألوان المركزي (app_colors.dart)**:
  - [ ] جميع ألوان التطبيق في مكان واحد
  - [ ] تجميع الألوان في فئات منطقية
  - [ ] دوال مساعدة للألوان الديناميكية
  - [ ] دعم الوضع الليلي والنهاري
  - [ ] ألوان الحالة والتدرجات اللونية

- [ ] **ملف الأيقونات المركزي (app_icons.dart)**:
  - [ ] جميع أيقونات التطبيق في مكان واحد
  - [ ] تجميع الأيقونات حسب الوظيفة
  - [ ] دوال مساعدة لإنشاء أيقونات مخصصة
  - [ ] دعم الأيقونات الدائرية والملونة

- [ ] **ملف أنماط النصوص المركزي (app_text_styles.dart)**:
  - [ ] جميع أنماط النصوص في مكان واحد
  - [ ] أحجام وأوزان خطوط موحدة
  - [ ] دعم الخطوط العربية (Cairo/Tajawal)
  - [ ] أنماط خاصة بالتطبيق (عملة، أزرار، حالة)
  - [ ] دوال مساعدة للوضع الداكن

- [ ] **ملف التزيينات المركزي (app_decorations.dart)**:
  - [ ] جميع التزيينات والحدود في مكان واحد
  - [ ] أنصاف أقطار ومسافات موحدة
  - [ ] تزيينات البطاقات والأزرار
  - [ ] تزيينات حقول الإدخال
  - [ ] دوال مساعدة لإنشاء تزيينات مخصصة

- [ ] **أمثلة عملية للاستخدام**:
  - [ ] بطاقة عميل باستخدام النظام المركزي
  - [ ] زر مخصص باستخدام النظام المركزي
  - [ ] حقل إدخال باستخدام النظام المركزي
  - [ ] أمثلة تغيير الثيم الكامل

- [ ] **هيكل المشروع المحدث**:
  - [ ] مجلد constants منظم بالملفات المركزية
  - [ ] تحديث pubspec.yaml بالمكتبات المطلوبة
  - [ ] ملف analysis_options.yaml لجودة الكود

#### المتطلبات السابقة - التصميم والبرمجة:
- [ ] نظام الألوان الجديد (أبيض + أزرق كحلي)
- [ ] دعم الوضع الليلي والنهاري الكامل
- [ ] ThemeController لإدارة الثيمات
- [ ] هيكل المشروع المحسن (Modular Architecture)
- [ ] العناصر المشتركة (CustomCard, CustomButton, CustomTextField)
- [ ] نظام التنقل المحسن مع Named Routes
- [ ] Bindings لكل وحدة منفصلة
- [ ] Controllers منفصلة لكل وحدة
- [ ] نظام حفظ تفضيلات الثيم
- [ ] تبديل تلقائي للثيم حسب النظام
- [ ] واجهات محدثة بالألوان الجديدة
- [ ] أيقونة تبديل الثيم في شريط التطبيق
- [ ] بطاقات الإحصائيات بالتصميم الجديد

#### المتطلبات السابقة - نظام الاشتراكات:
- [ ] نظام الفترة التجريبية التلقائية (30 يوم)
- [ ] شاشة عرض حالة الاشتراك مع العداد التنازلي
- [ ] نظام التذكيرات التلقائية (7، 3، 1 أيام)
- [ ] شاشة انتهاء الاشتراك مع معلومات التواصل
- [ ] خدمة الحصول على معرف الجهاز الفريد
- [ ] نظام إنشاء حسابات العملاء من قبل المالك فقط
- [ ] خدمة إرسال بيانات الدخول عبر SMS/WhatsApp
- [ ] مراقبة حالة الاشتراك كل ساعة
- [ ] منع الوصول التلقائي عند انتهاء الاشتراك
- [ ] لوحة تحكم إدارية لتفعيل الاشتراكات

---

*هذه الوثيقة تحتوي على جميع المتطلبات التقنية والوظيفية اللازمة لتطوير تطبيق "دائن مدين" بشكل احترافي ومتكامل، مع التحديثات الأخيرة لنظام الاشتراكات وإدارة المستخدمين. يُرجى الالتزام بجميع المواصفات المذكورة لضمان جودة المنتج النهائي.*
```